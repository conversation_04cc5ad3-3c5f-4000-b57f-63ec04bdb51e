import { ReactNode } from 'react'

interface BadgeProps {
  children: ReactNode
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
  className?: string
}

export default function Badge({
  children,
  variant = 'default',
  size = 'md',
  rounded = true,
  className = ''
}: BadgeProps) {
  const baseClasses = 'inline-flex items-center font-medium'
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    primary: 'bg-primary-100 text-primary-800',
    secondary: 'bg-secondary-100 text-secondary-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800'
  }
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  }
  
  const roundedClass = rounded ? 'rounded-full' : 'rounded'
  
  return (
    <span
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${roundedClass}
        ${className}
      `}
    >
      {children}
    </span>
  )
}

// Specialized badge components
export function StatusBadge({ 
  status, 
  className = '' 
}: { 
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  className?: string 
}) {
  const statusConfig = {
    pending: { variant: 'warning' as const, label: 'في الانتظار' },
    confirmed: { variant: 'info' as const, label: 'مؤكد' },
    processing: { variant: 'primary' as const, label: 'قيد التجهيز' },
    shipped: { variant: 'secondary' as const, label: 'تم الشحن' },
    delivered: { variant: 'success' as const, label: 'تم التسليم' },
    cancelled: { variant: 'error' as const, label: 'ملغي' }
  }
  
  const config = statusConfig[status]
  
  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  )
}

export function DiscountBadge({ 
  percentage, 
  className = '' 
}: { 
  percentage: number
  className?: string 
}) {
  return (
    <Badge variant="error" size="sm" className={className}>
      -{percentage}%
    </Badge>
  )
}

export function NewBadge({ className = '' }: { className?: string }) {
  return (
    <Badge variant="success" size="sm" className={className}>
      جديد
    </Badge>
  )
}

export function FreeShippingBadge({ className = '' }: { className?: string }) {
  return (
    <Badge variant="primary" size="sm" className={className}>
      شحن مجاني
    </Badge>
  )
}

export function StockBadge({ 
  inStock, 
  quantity, 
  className = '' 
}: { 
  inStock: boolean
  quantity?: number
  className?: string 
}) {
  if (!inStock) {
    return (
      <Badge variant="error" size="sm" className={className}>
        غير متوفر
      </Badge>
    )
  }
  
  if (quantity !== undefined && quantity <= 5) {
    return (
      <Badge variant="warning" size="sm" className={className}>
        {quantity} متبقي
      </Badge>
    )
  }
  
  return (
    <Badge variant="success" size="sm" className={className}>
      متوفر
    </Badge>
  )
}
