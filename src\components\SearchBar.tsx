'use client'

import { useState, useRef, useEffect } from 'react'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { debounce } from '@/lib/utils'

interface SearchResult {
  id: number
  name: string
  category: string
  price: number
  image: string
}

interface SearchBarProps {
  onSearch?: (query: string) => void
  placeholder?: string
  className?: string
}

export default function SearchBar({ 
  onSearch, 
  placeholder = "بحث...", 
  className = "" 
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Mock search results
  const mockResults: SearchResult[] = [
    {
      id: 1,
      name: 'سماعات لاسلكية عالية الجودة',
      category: 'إلكترونيات',
      price: 299,
      image: '/images/items/1.jpg'
    },
    {
      id: 2,
      name: 'ساعة ذكية متطورة',
      category: 'إلكترونيات',
      price: 599,
      image: '/images/items/2.jpg'
    },
    {
      id: 3,
      name: 'حقيبة ظهر عملية وأنيقة',
      category: 'حقائب',
      price: 149,
      image: '/images/items/3.jpg'
    }
  ]

  // Debounced search function
  const debouncedSearch = debounce(async (searchQuery: string) => {
    if (searchQuery.length < 2) {
      setResults([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      const filteredResults = mockResults.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setResults(filteredResults)
      setIsLoading(false)
    }, 300)
  }, 300)

  useEffect(() => {
    debouncedSearch(query)
  }, [query])

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setIsOpen(value.length > 0)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (query.trim()) {
      onSearch?.(query.trim())
      setIsOpen(false)
      inputRef.current?.blur()
    }
  }

  const handleResultClick = (result: SearchResult) => {
    setQuery(result.name)
    setIsOpen(false)
    onSearch?.(result.name)
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    inputRef.current?.focus()
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(query.length > 0)}
          placeholder={placeholder}
          className="w-full px-4 py-2 pr-12 pl-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
        
        {/* Clear button */}
        {query && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        )}
        
        {/* Search button */}
        <button
          type="submit"
          className="absolute left-0 top-0 h-full px-4 bg-primary-600 text-white rounded-l-lg hover:bg-primary-700 transition-colors"
        >
          <MagnifyingGlassIcon className="w-5 h-5" />
        </button>
      </form>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">جاري البحث...</p>
            </div>
          ) : results.length > 0 ? (
            <div>
              <div className="p-2 border-b border-gray-100">
                <p className="text-xs text-gray-500">نتائج البحث ({results.length})</p>
              </div>
              {results.map((result) => (
                <button
                  key={result.id}
                  onClick={() => handleResultClick(result)}
                  className="w-full p-3 flex items-center space-x-3 space-x-reverse hover:bg-gray-50 transition-colors text-right"
                >
                  <img
                    src={result.image}
                    alt={result.name}
                    className="w-10 h-10 object-cover rounded"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {result.name}
                    </p>
                    <p className="text-xs text-gray-500">{result.category}</p>
                  </div>
                  <div className="text-sm font-medium text-primary-600">
                    {result.price} ج.م
                  </div>
                </button>
              ))}
            </div>
          ) : query.length >= 2 ? (
            <div className="p-4 text-center">
              <p className="text-sm text-gray-500">لا توجد نتائج للبحث "{query}"</p>
            </div>
          ) : null}
          
          {query.length >= 2 && (
            <div className="p-2 border-t border-gray-100">
              <button
                onClick={() => {
                  onSearch?.(query)
                  setIsOpen(false)
                }}
                className="w-full text-right p-2 text-sm text-primary-600 hover:bg-primary-50 rounded transition-colors"
              >
                عرض جميع النتائج لـ "{query}"
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
