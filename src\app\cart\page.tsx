'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import Layout from '@/components/Layout'
import { useCart } from '@/contexts/CartContext'
import {
  TrashIcon,
  PlusIcon,
  MinusIcon,
  ShoppingBagIcon,
  TruckIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'

export default function CartPage() {
  const router = useRouter()
  const { items: cartItems, totalItems, totalPrice, updateQuantity, removeItem } = useCart()
  const [promoCode, setPromoCode] = useState('')
  const [discount, setDiscount] = useState(0)

  const handleUpdateQuantity = (id: string, newQuantity: number) => {
    updateQuantity(id, newQuantity)
  }

  const handleRemoveItem = (id: string) => {
    removeItem(id)
  }

  const applyPromoCode = () => {
    // Mock promo code logic
    if (promoCode.toLowerCase() === 'save10') {
      setDiscount(0.1) // 10% discount
    } else if (promoCode.toLowerCase() === 'save20') {
      setDiscount(0.2) // 20% discount
    } else {
      setDiscount(0)
      alert('كود الخصم غير صحيح')
    }
  }

  const subtotal = totalPrice
  const discountAmount = subtotal * discount
  const shipping = subtotal > 500 ? 0 : 50 // Free shipping over 500 EGP
  const total = subtotal - discountAmount + shipping

  if (cartItems.length === 0) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <ShoppingBagIcon className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">سلة التسوق فارغة</h1>
            <p className="text-gray-600 mb-8">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
            <Link href="/store" className="btn-primary">
              تسوق الآن
            </Link>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">سلة التسوق</h1>
          <p className="text-gray-600">{cartItems.length} منتج في السلة</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {cartItems.map((item, index) => (
                <div key={item.id} className={`p-6 ${index !== cartItems.length - 1 ? 'border-b border-gray-200' : ''}`}>
                  <div className="flex items-center space-x-4 space-x-reverse">
                    {/* Product Image */}
                    <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover"
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <Link href={`/products/${item.id}`} className="text-lg font-medium text-gray-900 hover:text-primary-600 line-clamp-2">
                        {item.name}
                      </Link>

                      <div className="flex items-center space-x-2 space-x-reverse mt-2">
                        <span className="text-lg font-bold text-gray-900">
                          {item.price.toLocaleString('ar-EG')} ج.م
                        </span>
                        {item.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {item.originalPrice.toLocaleString('ar-EG')} ج.م
                          </span>
                        )}
                      </div>

                      {!item.inStock && (
                        <p className="text-red-600 text-sm mt-1">غير متوفر في المخزن</p>
                      )}
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="flex items-center border border-gray-300 rounded-lg">
                        <button
                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                          className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
                          disabled={!item.inStock}
                        >
                          <MinusIcon className="w-4 h-4" />
                        </button>
                        <span className="px-4 py-2 border-x border-gray-300 min-w-[3rem] text-center">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                          className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
                          disabled={!item.inStock}
                        >
                          <PlusIcon className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="p-2 text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>

                    {/* Item Total */}
                    <div className="text-left">
                      <p className="text-lg font-bold text-gray-900">
                        {(item.price * item.quantity).toLocaleString('ar-EG')} ج.م
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Continue Shopping */}
            <div className="mt-6">
              <Link href="/store" className="text-primary-600 hover:text-primary-700 font-medium">
                ← متابعة التسوق
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">ملخص الطلب</h2>

              {/* Promo Code */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كود الخصم
                </label>
                <div className="flex space-x-2 space-x-reverse">
                  <input
                    type="text"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                    placeholder="أدخل كود الخصم"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <button
                    onClick={applyPromoCode}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    تطبيق
                  </button>
                </div>
                {discount > 0 && (
                  <p className="text-green-600 text-sm mt-2">
                    تم تطبيق خصم {(discount * 100).toFixed(0)}%
                  </p>
                )}
              </div>

              {/* Order Details */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي</span>
                  <span className="font-medium">{subtotal.toLocaleString('ar-EG')} ج.م</span>
                </div>

                {discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>الخصم</span>
                    <span>-{discountAmount.toLocaleString('ar-EG')} ج.م</span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-600">الشحن</span>
                  <span className="font-medium">
                    {shipping === 0 ? 'مجاني' : `${shipping} ج.م`}
                  </span>
                </div>

                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span>المجموع الكلي</span>
                    <span>{total.toLocaleString('ar-EG')} ج.م</span>
                  </div>
                </div>
              </div>

              {/* Checkout Button */}
              <button
                onClick={() => router.push('/checkout')}
                className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors mb-4"
              >
                إتمام الطلب
              </button>

              {/* Security Features */}
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <TruckIcon className="w-4 h-4 text-primary-600" />
                  <span>شحن مجاني للطلبات أكثر من 500 ج.م</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <ShieldCheckIcon className="w-4 h-4 text-primary-600" />
                  <span>دفع آمن ومضمون</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recommended Products */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">قد يعجبك أيضاً</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* This would be populated with recommended products */}
            <div className="text-center py-12 col-span-full">
              <p className="text-gray-500">المنتجات المقترحة ستظهر هنا</p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
