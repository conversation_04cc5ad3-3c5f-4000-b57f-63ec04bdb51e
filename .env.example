# Environment Variables Example
# Copy this file to .env.local and fill in your values

# App Configuration
NEXT_PUBLIC_APP_NAME="جريت كارت"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# API Configuration
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/greatcart_db"

# Authentication
NEXTAUTH_SECRET="your-super-secret-key-here-change-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Payment Gateways
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key_here"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret_here"

# Email Service (when implemented)
# EMAIL_SERVER_HOST="smtp.example.com"
# EMAIL_SERVER_PORT=587
# EMAIL_SERVER_USER="<EMAIL>"
# EMAIL_SERVER_PASSWORD="your_password"

# File Upload (when implemented)
# CLOUDINARY_CLOUD_NAME="your_cloud_name"
# CLOUDINARY_API_KEY="your_api_key"
# CLOUDINARY_API_SECRET="your_api_secret"

# Analytics (when implemented)
# GOOGLE_ANALYTICS_ID="your_ga_id"

# Social Login (when implemented)
# GOOGLE_CLIENT_ID="your_google_client_id"
# GOOGLE_CLIENT_SECRET="your_google_client_secret"
# FACEBOOK_CLIENT_ID="your_facebook_client_id"
# FACEBOOK_CLIENT_SECRET="your_facebook_client_secret"
