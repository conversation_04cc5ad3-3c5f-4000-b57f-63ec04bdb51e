# تطبيق التجارة الإلكترونية - جريت كارت

تطبيق تجارة إلكترونية متطور ومتجاوب مبني باستخدام React.js، Next.js، و Tailwind CSS مع دعم كامل للغة العربية (RTL).

## المميزات

### 🎨 التصميم والواجهة
- تصميم متجاوب مع جميع الأجهزة (Desktop, Tablet, Mobile)
- دعم كامل للغة العربية مع اتجاه RTL
- واجهة مستخدم حديثة وأنيقة
- تأثيرات بصرية سلسة وانتقالات ناعمة

### 🛍️ ميزات التسوق
- عرض المنتجات في شبكة أو قائمة
- فلترة المنتجات حسب الفئة والسعر
- ترتيب المنتجات (الأحدث، السعر، التقييم)
- صفحة تفاصيل المنتج مع معرض صور
- سلة تسوق تفاعلية
- نظام المفضلة
- كوبونات الخصم

### 👤 إدارة المستخدمين
- تسجيل الدخول والتسجيل
- تسجيل الدخول عبر وسائل التواصل الاجتماعي
- استعادة كلمة المرور
- ملف المستخدم الشخصي

### 📱 التوافق مع الأجهزة
- متوافق مع أجهزة Android و iOS
- تصميم متجاوب لجميع أحجام الشاشات
- تحسين الأداء للأجهزة المحمولة
- دعم اللمس والإيماءات

## التقنيات المستخدمة

- **Frontend Framework**: Next.js 14 (App Router)
- **UI Library**: React 18
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Language**: TypeScript
- **Package Manager**: npm/yarn

## متطلبات التشغيل

- Node.js 18.0 أو أحدث
- npm أو yarn

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd ecommerce-app
```

### 2. تثبيت التبعيات
```bash
npm install
# أو
yarn install
```

### 3. تشغيل المشروع في وضع التطوير
```bash
npm run dev
# أو
yarn dev
```

### 4. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:3000`

## بناء المشروع للإنتاج

```bash
npm run build
npm start
# أو
yarn build
yarn start
```

## هيكل المشروع

```
src/
├── app/                    # صفحات التطبيق (App Router)
│   ├── page.tsx           # الصفحة الرئيسية
│   ├── store/             # صفحة المتجر
│   ├── product/[id]/      # صفحة تفاصيل المنتج
│   ├── cart/              # صفحة سلة التسوق
│   ├── signin/            # صفحة تسجيل الدخول
│   ├── register/          # صفحة التسجيل
│   ├── layout.tsx         # التخطيط الرئيسي
│   └── globals.css        # الأنماط العامة
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── Header.tsx         # رأس الصفحة
│   ├── Footer.tsx         # تذييل الصفحة
│   ├── Layout.tsx         # مكون التخطيط
│   └── ProductCard.tsx    # بطاقة المنتج
└── public/               # الملفات الثابتة
    └── images/           # الصور
```

## الصفحات المتاحة

- **الصفحة الرئيسية** (`/`) - عرض المنتجات المميزة والفئات
- **المتجر** (`/store`) - عرض جميع المنتجات مع الفلاتر
- **تفاصيل المنتج** (`/product/[id]`) - تفاصيل المنتج ومعرض الصور
- **سلة التسوق** (`/cart`) - إدارة المنتجات في السلة
- **تسجيل الدخول** (`/signin`) - تسجيل دخول المستخدمين
- **التسجيل** (`/register`) - إنشاء حساب جديد

## المميزات التقنية

### دعم RTL
- اتجاه النص من اليمين لليسار
- تخطيط متجاوب مع الاتجاه العربي
- خطوط عربية محسنة

### الأداء
- تحسين الصور مع Next.js Image
- تحميل كسول للمكونات
- تحسين CSS مع Tailwind

### إمكانية الوصول
- دعم قارئات الشاشة
- تنقل بلوحة المفاتيح
- ألوان متباينة للوضوح

## التخصيص

### الألوان
يمكن تخصيص الألوان في ملف `tailwind.config.js`:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        // ألوان مخصصة
      }
    }
  }
}
```

### الخطوط
تم تكوين الخطوط العربية في `globals.css`:

```css
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
```

## المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.

---

تم تطوير هذا المشروع بـ ❤️ لمجتمع المطورين العرب
