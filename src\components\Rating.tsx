'use client'

import { useState } from 'react'
import { StarIcon } from '@heroicons/react/24/solid'
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline'

interface RatingProps {
  value: number
  max?: number
  size?: 'sm' | 'md' | 'lg'
  readonly?: boolean
  showValue?: boolean
  showCount?: boolean
  count?: number
  onChange?: (rating: number) => void
  className?: string
}

export default function Rating({
  value,
  max = 5,
  size = 'md',
  readonly = true,
  showValue = false,
  showCount = false,
  count,
  onChange,
  className = ''
}: RatingProps) {
  const [hoverValue, setHoverValue] = useState<number | null>(null)

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const handleClick = (rating: number) => {
    if (!readonly && onChange) {
      onChange(rating)
    }
  }

  const handleMouseEnter = (rating: number) => {
    if (!readonly) {
      setHoverValue(rating)
    }
  }

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverValue(null)
    }
  }

  const displayValue = hoverValue !== null ? hoverValue : value
  const roundedValue = Math.round(displayValue * 2) / 2 // Round to nearest 0.5

  return (
    <div className={`flex items-center space-x-1 space-x-reverse ${className}`}>
      <div className="flex items-center">
        {Array.from({ length: max }, (_, index) => {
          const starValue = index + 1
          const isFilled = starValue <= Math.floor(roundedValue)
          const isHalfFilled = starValue === Math.ceil(roundedValue) && roundedValue % 1 !== 0

          return (
            <button
              key={index}
              type="button"
              onClick={() => handleClick(starValue)}
              onMouseEnter={() => handleMouseEnter(starValue)}
              onMouseLeave={handleMouseLeave}
              disabled={readonly}
              className={`relative ${readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110'} transition-transform ${sizeClasses[size]}`}
              aria-label={`تقييم ${starValue} من ${max}`}
            >
              {isFilled || isHalfFilled ? (
                <div className="relative">
                  <StarIcon className={`${sizeClasses[size]} text-yellow-400`} />
                  {isHalfFilled && (
                    <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
                      <StarIcon className={`${sizeClasses[size]} text-yellow-400`} />
                    </div>
                  )}
                </div>
              ) : (
                <StarOutlineIcon className={`${sizeClasses[size]} text-gray-300`} />
              )}
            </button>
          )
        })}
      </div>

      {showValue && (
        <span className="text-sm font-medium text-gray-700 mr-2">
          {value.toFixed(1)}
        </span>
      )}

      {showCount && count !== undefined && (
        <span className="text-sm text-gray-500 mr-1">
          ({count.toLocaleString('ar-EG')} تقييم)
        </span>
      )}
    </div>
  )
}

// Rating Summary Component
interface RatingSummaryProps {
  averageRating: number
  totalReviews: number
  ratingDistribution: { [key: number]: number }
  className?: string
}

export function RatingSummary({
  averageRating,
  totalReviews,
  ratingDistribution,
  className = ''
}: RatingSummaryProps) {
  const maxCount = Math.max(...Object.values(ratingDistribution))

  return (
    <div className={`bg-white p-6 rounded-lg border border-gray-200 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center space-x-2 space-x-reverse mb-2">
            <span className="text-3xl font-bold text-gray-900">
              {averageRating.toFixed(1)}
            </span>
            <Rating value={averageRating} readonly showValue={false} />
          </div>
          <p className="text-sm text-gray-600">
            {totalReviews.toLocaleString('ar-EG')} تقييم
          </p>
        </div>
      </div>

      <div className="space-y-3">
        {[5, 4, 3, 2, 1].map((rating) => {
          const count = ratingDistribution[rating] || 0
          const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0

          return (
            <div key={rating} className="flex items-center space-x-3 space-x-reverse">
              <div className="flex items-center space-x-1 space-x-reverse min-w-0">
                <span className="text-sm text-gray-600">{rating}</span>
                <StarIcon className="w-4 h-4 text-yellow-400" />
              </div>
              
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              
              <span className="text-sm text-gray-600 min-w-0">
                {count.toLocaleString('ar-EG')}
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Interactive Rating Component for reviews
interface InteractiveRatingProps {
  onRatingChange: (rating: number) => void
  initialRating?: number
  label?: string
  required?: boolean
  className?: string
}

export function InteractiveRating({
  onRatingChange,
  initialRating = 0,
  label = 'التقييم',
  required = false,
  className = ''
}: InteractiveRatingProps) {
  const [rating, setRating] = useState(initialRating)
  const [hoverRating, setHoverRating] = useState<number | null>(null)

  const handleRatingChange = (newRating: number) => {
    setRating(newRating)
    onRatingChange(newRating)
  }

  const ratingLabels = {
    1: 'سيء جداً',
    2: 'سيء',
    3: 'متوسط',
    4: 'جيد',
    5: 'ممتاز'
  }

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="flex items-center space-x-4 space-x-reverse">
        <Rating
          value={rating}
          readonly={false}
          size="lg"
          onChange={handleRatingChange}
        />
        
        {(hoverRating || rating) > 0 && (
          <span className="text-sm text-gray-600">
            {ratingLabels[(hoverRating || rating) as keyof typeof ratingLabels]}
          </span>
        )}
      </div>
    </div>
  )
}
