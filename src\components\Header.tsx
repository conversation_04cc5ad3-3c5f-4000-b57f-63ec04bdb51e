'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import SearchBar from './SearchBar'
import { useCart } from '@/contexts/CartContext'
import {
  Bars3Icon,
  ShoppingCartIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { totalItems } = useCart()
  const [isCategoryOpen, setIsCategoryOpen] = useState(false)

  const categories = [
    'الآلات / قطع ميكانيكية / أدوات',
    'الإلكترونيات الاستهلاكية / الأجهزة المنزلية',
    'السيارات / النقل',
    'الملابس / المنسوجات / الساعات',
    'المنزل والحديقة / البناء / الإضاءة',
    'الجمال والعناية الشخصية / الصحة'
  ]

  return (
    <header className="bg-white shadow-sm">
      {/* Top Navigation */}
      <nav className="border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-12">
            <div className="hidden md:flex items-center space-x-6 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-sm text-gray-600">العربية</span>
                <ChevronDownIcon className="w-4 h-4 text-gray-400" />
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-sm text-gray-600">ج.م</span>
                <ChevronDownIcon className="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <div className="flex items-center space-x-6 space-x-reverse">
              <Link href="#" className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-primary-600">
                <EnvelopeIcon className="w-4 h-4" />
                <span>البريد الإلكتروني</span>
              </Link>
              <Link href="#" className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-primary-600">
                <PhoneIcon className="w-4 h-4" />
                <span>اتصل بنا</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center">
              <Image
                src="/images/logo.png"
                alt="جريت كارت"
                width={120}
                height={40}
                className="h-10 w-auto"
              />
            </Link>
          </div>

          {/* Categories Dropdown */}
          <div className="hidden lg:block relative">
            <button
              onClick={() => setIsCategoryOpen(!isCategoryOpen)}
              className="flex items-center space-x-2 space-x-reverse bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Bars3Icon className="w-5 h-5" />
              <span>جميع الفئات</span>
              <ChevronDownIcon className="w-4 h-4" />
            </button>

            {isCategoryOpen && (
              <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                {categories.map((category, index) => (
                  <Link
                    key={index}
                    href={`/category/${index}`}
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                  >
                    {category}
                  </Link>
                ))}
              </div>
            )}
          </div>

          {/* Store Link */}
          <Link href="/store" className="hidden lg:block btn-outline">
            المتجر
          </Link>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-4">
            <SearchBar
              onSearch={(query) => console.log('Search:', query)}
              placeholder="بحث..."
            />
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="hidden md:block text-right">
              <div className="text-xs text-gray-500">مرحباً بالزائر!</div>
              <div className="flex items-center space-x-2 space-x-reverse text-sm">
                <Link href="/signin" className="text-gray-700 hover:text-primary-600">
                  تسجيل الدخول
                </Link>
                <span className="text-gray-400">|</span>
                <Link href="/register" className="text-gray-700 hover:text-primary-600">
                  التسجيل
                </Link>
              </div>
            </div>

            {/* Cart */}
            <Link href="/cart" className="relative p-2 text-gray-700 hover:text-primary-600">
              <ShoppingCartIcon className="w-6 h-6" />
              {totalItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {totalItems}
                </span>
              )}
            </Link>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-gray-700 hover:text-primary-600"
            >
              <Bars3Icon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden mt-4 border-t border-gray-200 pt-4">
            <div className="space-y-2">
              <Link href="/store" className="block py-2 text-gray-700 hover:text-primary-600">
                المتجر
              </Link>
              <div className="border-t border-gray-200 pt-2">
                <div className="text-sm font-medium text-gray-900 mb-2">الفئات</div>
                {categories.map((category, index) => (
                  <Link
                    key={index}
                    href={`/category/${index}`}
                    className="block py-1 text-sm text-gray-600 hover:text-primary-600"
                  >
                    {category}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
