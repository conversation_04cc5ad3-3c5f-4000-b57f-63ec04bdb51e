import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const product = await db.product.findUnique({
      where: {
        id: params.id,
        isActive: true,
      },
      include: {
        category: {
          select: { name: true, slug: true }
        },
        images: {
          orderBy: { sortOrder: 'asc' }
        },
        variants: true,
        reviews: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: { reviews: true, orderItems: true }
        }
      }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'المنتج غير موجود'
      }, { status: 404 })
    }

    // Calculate average rating
    const avgRating = product.reviews.length > 0
      ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
      : 0

    // Calculate rating distribution
    const ratingDistribution = product.reviews.reduce((acc, review) => {
      acc[review.rating] = (acc[review.rating] || 0) + 1
      return acc
    }, {} as Record<number, number>)

    // Get related products
    const relatedProducts = await db.product.findMany({
      where: {
        categoryId: product.categoryId,
        id: { not: product.id },
        isActive: true,
      },
      include: {
        images: {
          take: 1,
          orderBy: { sortOrder: 'asc' }
        },
        reviews: {
          select: { rating: true }
        }
      },
      take: 4,
    })

    const relatedWithRatings = relatedProducts.map(p => {
      const avgRating = p.reviews.length > 0
        ? p.reviews.reduce((sum, review) => sum + review.rating, 0) / p.reviews.length
        : 0

      return {
        ...p,
        averageRating: Math.round(avgRating * 10) / 10,
        image: p.images[0]?.url || null,
        reviews: undefined,
      }
    })

    const productData = {
      ...product,
      averageRating: Math.round(avgRating * 10) / 10,
      reviewCount: product._count.reviews,
      salesCount: product._count.orderItems,
      ratingDistribution,
      relatedProducts: relatedWithRatings,
      _count: undefined,
    }

    return NextResponse.json({
      success: true,
      data: productData
    })

  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب المنتج'
    }, { status: 500 })
  }
}
