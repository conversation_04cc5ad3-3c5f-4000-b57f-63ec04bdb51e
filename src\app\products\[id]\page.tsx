'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Layout from '@/components/Layout'
import ProductCard from '@/components/ProductCard'
import {
  StarIcon,
  HeartIcon,
  ShareIcon,
  ShoppingCartIcon,
  TruckIcon,
  ShieldCheckIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import { formatCurrency } from '@/lib/utils'
import { useCart } from '@/contexts/CartContext'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  images: string[]
  rating: number
  reviews: number
  description: string
  features: string[]
  specifications: { [key: string]: string }
  category: string
  brand: string
  inStock: boolean
  stockCount: number
  sku: string
}

interface Review {
  id: string
  userName: string
  rating: number
  comment: string
  date: string
  verified: boolean
}

export default function ProductPage() {
  const params = useParams()
  const { addItem } = useCart()
  const [product, setProduct] = useState<Product | null>(null)
  const [reviews, setReviews] = useState<Review[]>([])
  const [relatedProducts, setRelatedProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [activeTab, setActiveTab] = useState('description')

  useEffect(() => {
    fetchProduct()
    fetchReviews()
    fetchRelatedProducts()
  }, [params.id])

  const fetchProduct = async () => {
    setLoading(true)
    // Mock API call
    setTimeout(() => {
      const mockProduct: Product = {
        id: params.id as string,
        name: 'سماعات لاسلكية عالية الجودة',
        price: 299,
        originalPrice: 399,
        images: [
          '/images/items/1.jpg',
          '/images/items/2.jpg',
          '/images/items/3.jpg',
          '/images/items/4.jpg'
        ],
        rating: 4.5,
        reviews: 128,
        description: 'سماعات لاسلكية متطورة تقدم جودة صوت استثنائية مع تقنية إلغاء الضوضاء النشطة. مصممة للراحة طوال اليوم مع بطارية تدوم حتى 30 ساعة.',
        features: [
          'تقنية إلغاء الضوضاء النشطة',
          'بطارية تدوم حتى 30 ساعة',
          'اتصال بلوتوث 5.0',
          'مقاومة للماء IPX4',
          'شحن سريع - 15 دقيقة = 3 ساعات تشغيل',
          'تحكم باللمس الذكي'
        ],
        specifications: {
          'نوع الاتصال': 'بلوتوث 5.0',
          'مدى الاتصال': '10 متر',
          'عمر البطارية': '30 ساعة',
          'وقت الشحن': '2 ساعة',
          'الوزن': '250 جرام',
          'مقاومة الماء': 'IPX4',
          'الألوان المتاحة': 'أسود، أبيض، أزرق'
        },
        category: 'إلكترونيات',
        brand: 'TechPro',
        inStock: true,
        stockCount: 15,
        sku: 'TP-WH-001'
      }
      setProduct(mockProduct)
      setLoading(false)
    }, 1000)
  }

  const fetchReviews = async () => {
    const mockReviews: Review[] = [
      {
        id: '1',
        userName: 'أحمد محمد',
        rating: 5,
        comment: 'سماعات رائعة! جودة الصوت ممتازة وإلغاء الضوضاء يعمل بشكل مثالي.',
        date: '2023-12-01',
        verified: true
      },
      {
        id: '2',
        userName: 'فاطمة علي',
        rating: 4,
        comment: 'مريحة جداً للاستخدام لفترات طويلة. البطارية تدوم كما هو مذكور.',
        date: '2023-11-28',
        verified: true
      },
      {
        id: '3',
        userName: 'محمد حسن',
        rating: 5,
        comment: 'أفضل استثمار! الصوت واضح والتصميم أنيق.',
        date: '2023-11-25',
        verified: false
      }
    ]
    setReviews(mockReviews)
  }

  const fetchRelatedProducts = async () => {
    const mockRelated = [
      {
        id: '2',
        name: 'ساعة ذكية متطورة',
        price: 599,
        originalPrice: 799,
        image: '/images/items/2.jpg',
        rating: 4.8,
        reviews: 89
      },
      {
        id: '4',
        name: 'كاميرا رقمية احترافية',
        price: 1299,
        originalPrice: 1599,
        image: '/images/items/4.jpg',
        rating: 4.9,
        reviews: 156
      }
    ]
    setRelatedProducts(mockRelated)
  }

  const addToCart = () => {
    if (product) {
      addItem({
        id: product.id,
        name: product.name,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.images[0],
        inStock: product.inStock,
        maxQuantity: product.stockCount,
        quantity: quantity
      })
    }
  }

  const buyNow = () => {
    addToCart()
    // Redirect to checkout
    window.location.href = '/checkout'
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  if (!product) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">المنتج غير موجود</h2>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 space-x-reverse">
            <li><a href="/" className="text-gray-500 hover:text-gray-700">الرئيسية</a></li>
            <li><span className="text-gray-400">/</span></li>
            <li><a href="/store" className="text-gray-500 hover:text-gray-700">المتجر</a></li>
            <li><span className="text-gray-400">/</span></li>
            <li><span className="text-gray-900">{product.name}</span></li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <Image
                src={product.images[selectedImage]}
                alt={product.name}
                width={600}
                height={600}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 ${
                    selectedImage === index ? 'border-primary-500' : 'border-transparent'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    width={150}
                    height={150}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <p className="text-gray-600">العلامة التجارية: {product.brand}</p>
              <p className="text-sm text-gray-500">رمز المنتج: {product.sku}</p>
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <StarIconSolid
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">
                {product.rating} ({product.reviews} تقييم)
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-3xl font-bold text-primary-600">
                {formatCurrency(product.price)}
              </span>
              {product.originalPrice && (
                <span className="text-xl text-gray-500 line-through">
                  {formatCurrency(product.originalPrice)}
                </span>
              )}
              {product.originalPrice && (
                <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                  وفر {formatCurrency(product.originalPrice - product.price)}
                </span>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2 space-x-reverse">
              {product.inStock ? (
                <>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="text-green-600">متوفر في المخزون ({product.stockCount} قطعة)</span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <span className="text-red-600">غير متوفر</span>
                </>
              )}
            </div>

            {/* Quantity & Actions */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4 space-x-reverse">
                <label className="text-sm font-medium text-gray-700">الكمية:</label>
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="px-3 py-2 text-gray-600 hover:text-gray-800"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                  <button
                    onClick={() => setQuantity(Math.min(product.stockCount, quantity + 1))}
                    className="px-3 py-2 text-gray-600 hover:text-gray-800"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse">
                <button
                  onClick={addToCart}
                  disabled={!product.inStock}
                  className="flex-1 bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ShoppingCartIcon className="w-5 h-5" />
                  <span>أضف للسلة</span>
                </button>
                <button
                  onClick={buyNow}
                  disabled={!product.inStock}
                  className="flex-1 bg-gray-900 text-white py-3 px-6 rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  اشتري الآن
                </button>
              </div>

              <div className="flex space-x-4 space-x-reverse">
                <button className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-gray-800">
                  <HeartIcon className="w-5 h-5" />
                  <span>أضف للمفضلة</span>
                </button>
                <button className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-gray-800">
                  <ShareIcon className="w-5 h-5" />
                  <span>مشاركة</span>
                </button>
              </div>
            </div>

            {/* Features */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">المميزات الرئيسية</h3>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-2 space-x-reverse">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Shipping Info */}
            <div className="border-t pt-6 space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <TruckIcon className="w-5 h-5 text-green-600" />
                <span className="text-sm text-gray-700">شحن مجاني للطلبات أكثر من 200 جنيه</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <ShieldCheckIcon className="w-5 h-5 text-blue-600" />
                <span className="text-sm text-gray-700">ضمان لمدة سنتين</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <ArrowPathIcon className="w-5 h-5 text-purple-600" />
                <span className="text-sm text-gray-700">إمكانية الإرجاع خلال 30 يوم</span>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="border-t pt-8 mb-12">
          <div className="flex space-x-8 space-x-reverse border-b">
            {['description', 'specifications', 'reviews'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab === 'description' && 'الوصف'}
                {tab === 'specifications' && 'المواصفات'}
                {tab === 'reviews' && 'التقييمات'}
              </button>
            ))}
          </div>

          <div className="py-6">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed">{product.description}</p>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-200">
                    <span className="font-medium text-gray-900">{key}</span>
                    <span className="text-gray-700">{value}</span>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                {reviews.map((review) => (
                  <div key={review.id} className="border-b border-gray-200 pb-6">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="font-medium text-gray-900">{review.userName}</span>
                        {review.verified && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                            مشتري موثق
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">{review.date}</span>
                    </div>
                    <div className="flex items-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <StarIconSolid
                          key={i}
                          className={`w-4 h-4 ${
                            i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-gray-700">{review.comment}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">منتجات ذات صلة</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
