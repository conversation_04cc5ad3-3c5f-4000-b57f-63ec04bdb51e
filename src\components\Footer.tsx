import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">جريت كارت</h3>
            <p className="text-gray-600 text-sm mb-4">
              واحدة من أكبر منصات التسوق عبر الإنترنت في المنطقة العربية
            </p>
            <div className="flex space-x-4 space-x-reverse">
              <Link href="#" className="text-gray-400 hover:text-primary-600">
                <span className="sr-only">Facebook</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-400 hover:text-primary-600">
                <span className="sr-only">Twitter</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-400 hover:text-primary-600">
                <span className="sr-only">Instagram</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">روابط سريعة</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-600 hover:text-primary-600 text-sm">
                  من نحن
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-primary-600 text-sm">
                  اتصل بنا
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-600 hover:text-primary-600 text-sm">
                  الأسئلة الشائعة
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-gray-600 hover:text-primary-600 text-sm">
                  الشحن والتوصيل
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">خدمة العملاء</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/support" className="text-gray-600 hover:text-primary-600 text-sm">
                  الدعم الفني
                </Link>
              </li>
              <li>
                <Link href="/returns" className="text-gray-600 hover:text-primary-600 text-sm">
                  سياسة الإرجاع
                </Link>
              </li>
              <li>
                <Link href="/warranty" className="text-gray-600 hover:text-primary-600 text-sm">
                  الضمان
                </Link>
              </li>
              <li>
                <Link href="/track-order" className="text-gray-600 hover:text-primary-600 text-sm">
                  تتبع الطلب
                </Link>
              </li>
            </ul>
          </div>

          {/* Payment Methods */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">طرق الدفع</h3>
            <div className="grid grid-cols-3 gap-2">
              <div className="bg-gray-100 p-2 rounded flex items-center justify-center">
                <span className="text-xs text-gray-600">Visa</span>
              </div>
              <div className="bg-gray-100 p-2 rounded flex items-center justify-center">
                <span className="text-xs text-gray-600">Master</span>
              </div>
              <div className="bg-gray-100 p-2 rounded flex items-center justify-center">
                <span className="text-xs text-gray-600">PayPal</span>
              </div>
              <div className="bg-gray-100 p-2 rounded flex items-center justify-center">
                <span className="text-xs text-gray-600">Apple Pay</span>
              </div>
              <div className="bg-gray-100 p-2 rounded flex items-center justify-center">
                <span className="text-xs text-gray-600">Google Pay</span>
              </div>
              <div className="bg-gray-100 p-2 rounded flex items-center justify-center">
                <span className="text-xs text-gray-600">نقداً</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-200 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex space-x-6 space-x-reverse mb-4 md:mb-0">
              <Link href="/privacy" className="text-gray-600 hover:text-primary-600 text-sm">
                سياسة الخصوصية
              </Link>
              <Link href="/terms" className="text-gray-600 hover:text-primary-600 text-sm">
                شروط الاستخدام
              </Link>
              <Link href="/returns-policy" className="text-gray-600 hover:text-primary-600 text-sm">
                سياسة الإرجاع
              </Link>
            </div>
            <div className="text-gray-600 text-sm">
              © 2023 جميع الحقوق محفوظة - جريت كارت
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
