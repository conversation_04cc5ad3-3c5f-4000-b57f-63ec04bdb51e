// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Model
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  firstName     String
  lastName      String
  phone         String?
  password      String
  avatar        String?
  emailVerified DateTime?
  isActive      Boolean   @default(true)
  role          UserRole  @default(CUSTOMER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  addresses     Address[]
  orders        Order[]
  reviews       Review[]
  cartItems     CartItem[]
  wishlistItems WishlistItem[]
  sessions      Session[]

  @@map("users")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Category Model
model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?
  image       String?
  icon        String?
  parentId    String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

// Product Model
model Product {
  id            String        @id @default(cuid())
  name          String
  slug          String        @unique
  description   String?
  shortDesc     String?
  price         Float
  originalPrice Float?
  sku           String        @unique
  stock         Int           @default(0)
  isActive      Boolean       @default(true)
  isFeatured    Boolean       @default(false)
  weight        Float?
  dimensions    String?
  brand         String?
  tags          String?
  metaTitle     String?
  metaDesc      String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  categoryId    String
  category      Category      @relation(fields: [categoryId], references: [id])
  images        ProductImage[]
  variants      ProductVariant[]
  reviews       Review[]
  cartItems     CartItem[]
  orderItems    OrderItem[]
  wishlistItems WishlistItem[]

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  alt       String?
  sortOrder Int     @default(0)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id        String  @id @default(cuid())
  name      String
  value     String
  price     Float
  stock     Int     @default(0)
  sku       String  @unique
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

// Address Model
model Address {
  id         String      @id @default(cuid())
  type       AddressType @default(HOME)
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String      @default("EG")
  phone      String?
  isDefault  Boolean     @default(false)
  userId     String
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  shippingOrders Order[] @relation("ShippingAddress")
  billingOrders  Order[] @relation("BillingAddress")

  @@map("addresses")
}

// Cart Model
model CartItem {
  id        String   @id @default(cuid())
  quantity  Int      @default(1)
  userId    String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

// Wishlist Model
model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

// Order Model
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  status          OrderStatus @default(PENDING)
  subtotal        Float
  discount        Float       @default(0)
  shipping        Float       @default(0)
  tax             Float       @default(0)
  total           Float
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?
  paymentId       String?
  notes           String?
  estimatedDelivery DateTime?
  deliveredAt     DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  userId            String
  user              User      @relation(fields: [userId], references: [id])
  shippingAddressId String
  shippingAddress   Address   @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  billingAddressId  String?
  billingAddress    Address?  @relation("BillingAddress", fields: [billingAddressId], references: [id])
  items             OrderItem[]
  payments          Payment[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Float
  orderId   String
  productId String

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Payment Model
model Payment {
  id            String        @id @default(cuid())
  amount        Float
  status        PaymentStatus @default(PENDING)
  method        String
  transactionId String?
  gatewayData   Json?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  orderId String
  order   Order  @relation(fields: [orderId], references: [id])

  @@map("payments")
}

// Review Model
model Review {
  id        String   @id @default(cuid())
  rating    Int
  title     String?
  comment   String
  images    String?
  verified  Boolean  @default(false)
  helpful   Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId    String
  productId String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

// Coupon Model
model Coupon {
  id          String     @id @default(cuid())
  code        String     @unique
  type        CouponType @default(PERCENTAGE)
  value       Float
  minAmount   Float?
  maxDiscount Float?
  usageLimit  Int?
  usageCount  Int        @default(0)
  isActive    Boolean    @default(true)
  startsAt    DateTime?
  expiresAt   DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@map("coupons")
}

// Enums
enum UserRole {
  CUSTOMER
  ADMIN
  SUPER_ADMIN
}

enum AddressType {
  HOME
  WORK
  OTHER
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  CANCELLED
}

enum CouponType {
  PERCENTAGE
  FIXED
}
