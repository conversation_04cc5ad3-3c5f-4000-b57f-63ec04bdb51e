'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import Layout from '@/components/Layout'
import { 
  CheckCircleIcon,
  TruckIcon,
  EnvelopeIcon,
  PhoneIcon,
  PrinterIcon,
  ShareIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'

interface OrderDetails {
  orderNumber: string
  orderDate: string
  estimatedDelivery: string
  status: string
  total: number
  paymentMethod: string
  shippingAddress: {
    name: string
    address: string
    city: string
    phone: string
  }
  items: Array<{
    id: string
    name: string
    price: number
    quantity: number
    image: string
  }>
}

export default function OrderConfirmationPage() {
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Mock API call to get order details
    setTimeout(() => {
      const mockOrder: OrderDetails = {
        orderNumber: 'ORD-2023-' + Math.random().toString(36).substr(2, 9).toUpperCase(),
        orderDate: new Date().toLocaleDateString('ar-EG'),
        estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString('ar-EG'),
        status: 'confirmed',
        total: 1197,
        paymentMethod: 'بطاقة ائتمان',
        shippingAddress: {
          name: 'أحمد محمد علي',
          address: 'شارع النيل، المعادي، القاهرة',
          city: 'القاهرة',
          phone: '+20 ************'
        },
        items: [
          {
            id: '1',
            name: 'سماعات لاسلكية عالية الجودة',
            price: 299,
            quantity: 2,
            image: '/images/items/1.jpg'
          },
          {
            id: '2',
            name: 'ساعة ذكية متطورة',
            price: 599,
            quantity: 1,
            image: '/images/items/2.jpg'
          }
        ]
      }
      setOrderDetails(mockOrder)
      setLoading(false)
    }, 1000)
  }, [])

  const handlePrint = () => {
    window.print()
  }

  const handleShare = () => {
    if (navigator.share && orderDetails) {
      navigator.share({
        title: 'تأكيد الطلب',
        text: `تم تأكيد طلبك رقم ${orderDetails.orderNumber}`,
        url: window.location.href
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('تم نسخ الرابط')
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  if (!orderDetails) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">لم يتم العثور على الطلب</h2>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">تم تأكيد طلبك بنجاح!</h1>
          <p className="text-gray-600">شكراً لك على ثقتك بنا. سنقوم بمعالجة طلبك في أقرب وقت.</p>
        </div>

        {/* Order Summary Card */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
          {/* Header */}
          <div className="bg-primary-50 px-6 py-4 border-b border-primary-100">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  طلب رقم: {orderDetails.orderNumber}
                </h2>
                <p className="text-sm text-gray-600">تاريخ الطلب: {orderDetails.orderDate}</p>
              </div>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  onClick={handlePrint}
                  className="p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg"
                >
                  <PrinterIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={handleShare}
                  className="p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg"
                >
                  <ShareIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Order Status */}
            <div className="mb-6">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircleIcon className="w-5 h-5 text-green-600" />
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">تم تأكيد الطلب</p>
                  <p className="text-sm text-gray-500">سيتم شحن طلبك قريباً</p>
                </div>
              </div>
            </div>

            {/* Delivery Info */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-3 space-x-reverse">
                <TruckIcon className="w-6 h-6 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-blue-900">التوصيل المتوقع</p>
                  <p className="text-sm text-blue-700">{orderDetails.estimatedDelivery}</p>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">المنتجات المطلوبة</h3>
              <div className="space-y-4">
                {orderDetails.items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 space-x-reverse">
                    <Image
                      src={item.image}
                      alt={item.name}
                      width={60}
                      height={60}
                      className="w-15 h-15 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{item.name}</h4>
                      <p className="text-sm text-gray-500">الكمية: {item.quantity}</p>
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(item.price * item.quantity)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Total */}
            <div className="border-t pt-4 mb-6">
              <div className="flex justify-between text-lg font-bold">
                <span>المجموع الإجمالي</span>
                <span>{formatCurrency(orderDetails.total)}</span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                طريقة الدفع: {orderDetails.paymentMethod}
              </p>
            </div>

            {/* Shipping Address */}
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">عنوان الشحن</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="font-medium text-gray-900">{orderDetails.shippingAddress.name}</p>
                <p className="text-gray-700">{orderDetails.shippingAddress.address}</p>
                <p className="text-gray-700">{orderDetails.shippingAddress.city}</p>
                <div className="flex items-center space-x-2 space-x-reverse mt-2">
                  <PhoneIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-700">{orderDetails.shippingAddress.phone}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">الخطوات التالية</h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-primary-600">1</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">تأكيد الطلب</p>
                <p className="text-sm text-gray-500">سنرسل لك رسالة تأكيد عبر البريد الإلكتروني</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">2</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">تجهيز الطلب</p>
                <p className="text-sm text-gray-500">سنقوم بتجهيز وتغليف منتجاتك بعناية</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">3</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">الشحن والتوصيل</p>
                <p className="text-sm text-gray-500">سنرسل لك رقم التتبع عند شحن الطلب</p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">هل تحتاج مساعدة؟</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3 space-x-reverse">
              <EnvelopeIcon className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-900">البريد الإلكتروني</p>
                <a href="mailto:<EMAIL>" className="text-sm text-primary-600 hover:text-primary-700">
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <PhoneIcon className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-900">خدمة العملاء</p>
                <a href="tel:+201234567890" className="text-sm text-primary-600 hover:text-primary-700">
                  +20 ************
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/store"
            className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 text-center font-medium"
          >
            متابعة التسوق
          </Link>
          <Link
            href="/account/orders"
            className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 text-center font-medium"
          >
            عرض طلباتي
          </Link>
        </div>

        {/* Email Notification */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
            <EnvelopeIcon className="w-4 h-4" />
            <span>تم إرسال تأكيد الطلب إلى بريدك الإلكتروني</span>
          </div>
        </div>
      </div>
    </Layout>
  )
}
