import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { z } from 'zod'

const addToCartSchema = z.object({
  productId: z.string(),
  quantity: z.number().int().positive().default(1),
})

const updateCartSchema = z.object({
  quantity: z.number().int().min(0),
})

// Get cart items
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: 'يجب تسجيل الدخول أولاً'
      }, { status: 401 })
    }

    const cartItems = await db.cartItem.findMany({
      where: { userId: session.user.id },
      include: {
        product: {
          include: {
            images: {
              take: 1,
              orderBy: { sortOrder: 'asc' }
            }
          }
        }
      }
    })

    const formattedItems = cartItems.map(item => ({
      id: item.id,
      quantity: item.quantity,
      product: {
        ...item.product,
        image: item.product.images[0]?.url || null,
        images: undefined,
      }
    }))

    const subtotal = formattedItems.reduce(
      (sum, item) => sum + (Number(item.product.price) * item.quantity),
      0
    )

    return NextResponse.json({
      success: true,
      data: {
        items: formattedItems,
        subtotal,
        itemCount: formattedItems.reduce((sum, item) => sum + item.quantity, 0)
      }
    })

  } catch (error) {
    console.error('Error fetching cart:', error)
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب السلة'
    }, { status: 500 })
  }
}

// Add item to cart
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: 'يجب تسجيل الدخول أولاً'
      }, { status: 401 })
    }

    const body = await request.json()
    const { productId, quantity } = addToCartSchema.parse(body)

    // Check if product exists and is active
    const product = await db.product.findUnique({
      where: { id: productId, isActive: true }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'المنتج غير موجود'
      }, { status: 404 })
    }

    // Check stock
    if (product.stock < quantity) {
      return NextResponse.json({
        success: false,
        message: 'الكمية المطلوبة غير متوفرة'
      }, { status: 400 })
    }

    // Check if item already exists in cart
    const existingItem = await db.cartItem.findUnique({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId
        }
      }
    })

    if (existingItem) {
      // Update quantity
      const newQuantity = existingItem.quantity + quantity
      
      if (product.stock < newQuantity) {
        return NextResponse.json({
          success: false,
          message: 'الكمية المطلوبة غير متوفرة'
        }, { status: 400 })
      }

      const updatedItem = await db.cartItem.update({
        where: { id: existingItem.id },
        data: { quantity: newQuantity },
        include: {
          product: {
            include: {
              images: { take: 1, orderBy: { sortOrder: 'asc' } }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        message: 'تم تحديث السلة',
        data: {
          ...updatedItem,
          product: {
            ...updatedItem.product,
            image: updatedItem.product.images[0]?.url || null,
            images: undefined,
          }
        }
      })
    } else {
      // Create new cart item
      const cartItem = await db.cartItem.create({
        data: {
          userId: session.user.id,
          productId,
          quantity
        },
        include: {
          product: {
            include: {
              images: { take: 1, orderBy: { sortOrder: 'asc' } }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        message: 'تم إضافة المنتج للسلة',
        data: {
          ...cartItem,
          product: {
            ...cartItem.product,
            image: cartItem.product.images[0]?.url || null,
            images: undefined,
          }
        }
      })
    }

  } catch (error) {
    console.error('Error adding to cart:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: 'خطأ في البيانات المدخلة',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إضافة المنتج للسلة'
    }, { status: 500 })
  }
}
