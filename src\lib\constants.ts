// App Configuration
export const APP_CONFIG = {
  name: 'جريت كارت',
  description: 'واحدة من أكبر منصات التسوق عبر الإنترنت',
  url: 'https://greatcart.com',
  locale: 'ar',
  currency: 'EGP',
  currencySymbol: 'ج.م',
  direction: 'rtl'
}

// API Configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  retries: 3
}

// Pagination
export const PAGINATION = {
  defaultLimit: 12,
  maxLimit: 100,
  defaultPage: 1
}

// Product Configuration
export const PRODUCT_CONFIG = {
  maxImages: 10,
  maxReviews: 100,
  defaultRating: 0,
  freeShippingThreshold: 500, // EGP
  maxQuantityPerItem: 10
}

// Categories
export const CATEGORIES = [
  {
    id: 1,
    name: 'إلكترونيات',
    slug: 'electronics',
    icon: '📱',
    subcategories: [
      'هواتف ذكية',
      'أجهزة لوحية',
      'أجهزة كمبيوتر',
      'سماعات',
      'كاميرات'
    ]
  },
  {
    id: 2,
    name: 'ملابس',
    slug: 'clothing',
    icon: '👕',
    subcategories: [
      'ملابس رجالية',
      'ملابس نسائية',
      'ملابس أطفال',
      'أحذية',
      'إكسسوارات'
    ]
  },
  {
    id: 3,
    name: 'منزل وحديقة',
    slug: 'home-garden',
    icon: '🏠',
    subcategories: [
      'أثاث',
      'ديكور',
      'أدوات مطبخ',
      'حديقة',
      'تنظيف'
    ]
  },
  {
    id: 4,
    name: 'رياضة',
    slug: 'sports',
    icon: '⚽',
    subcategories: [
      'ملابس رياضية',
      'معدات رياضية',
      'مكملات غذائية',
      'أحذية رياضية'
    ]
  },
  {
    id: 5,
    name: 'جمال وعناية',
    slug: 'beauty',
    icon: '💄',
    subcategories: [
      'مكياج',
      'عناية بالبشرة',
      'عناية بالشعر',
      'عطور',
      'أدوات تجميل'
    ]
  },
  {
    id: 6,
    name: 'كتب',
    slug: 'books',
    icon: '📚',
    subcategories: [
      'كتب عربية',
      'كتب إنجليزية',
      'كتب أطفال',
      'كتب تعليمية',
      'روايات'
    ]
  }
]

// Sort Options
export const SORT_OPTIONS = [
  { value: 'newest', label: 'الأحدث' },
  { value: 'price-low', label: 'السعر: من الأقل للأعلى' },
  { value: 'price-high', label: 'السعر: من الأعلى للأقل' },
  { value: 'rating', label: 'الأعلى تقييماً' },
  { value: 'popular', label: 'الأكثر شعبية' },
  { value: 'name-asc', label: 'الاسم: أ-ي' },
  { value: 'name-desc', label: 'الاسم: ي-أ' }
]

// Price Ranges
export const PRICE_RANGES = [
  { min: 0, max: 100, label: 'أقل من 100 ج.م' },
  { min: 100, max: 500, label: '100 - 500 ج.م' },
  { min: 500, max: 1000, label: '500 - 1000 ج.م' },
  { min: 1000, max: 2000, label: '1000 - 2000 ج.م' },
  { min: 2000, max: 5000, label: '2000 - 5000 ج.م' },
  { min: 5000, max: Infinity, label: 'أكثر من 5000 ج.م' }
]

// Order Status
export const ORDER_STATUS = {
  pending: { label: 'في الانتظار', color: 'yellow' },
  confirmed: { label: 'مؤكد', color: 'blue' },
  processing: { label: 'قيد التجهيز', color: 'purple' },
  shipped: { label: 'تم الشحن', color: 'indigo' },
  delivered: { label: 'تم التسليم', color: 'green' },
  cancelled: { label: 'ملغي', color: 'red' }
}

// Payment Methods
export const PAYMENT_METHODS = [
  {
    id: 'card',
    name: 'بطاقة ائتمان',
    icon: '💳',
    description: 'Visa, Mastercard, American Express'
  },
  {
    id: 'paypal',
    name: 'PayPal',
    icon: '🅿️',
    description: 'دفع آمن عبر PayPal'
  },
  {
    id: 'bank',
    name: 'تحويل بنكي',
    icon: '🏦',
    description: 'تحويل مباشر من البنك'
  },
  {
    id: 'cash',
    name: 'الدفع عند الاستلام',
    icon: '💵',
    description: 'ادفع عند وصول الطلب'
  }
]

// Shipping Methods
export const SHIPPING_METHODS = [
  {
    id: 'standard',
    name: 'شحن عادي',
    price: 50,
    duration: '3-5 أيام عمل',
    description: 'شحن عادي خلال أيام العمل'
  },
  {
    id: 'express',
    name: 'شحن سريع',
    price: 100,
    duration: '1-2 أيام عمل',
    description: 'شحن سريع خلال يوم أو يومين'
  },
  {
    id: 'overnight',
    name: 'شحن فوري',
    price: 200,
    duration: '24 ساعة',
    description: 'توصيل خلال 24 ساعة'
  }
]

// Social Media Links
export const SOCIAL_LINKS = {
  facebook: 'https://facebook.com/greatcart',
  twitter: 'https://twitter.com/greatcart',
  instagram: 'https://instagram.com/greatcart',
  youtube: 'https://youtube.com/greatcart',
  linkedin: 'https://linkedin.com/company/greatcart'
}

// Contact Information
export const CONTACT_INFO = {
  phone: '+20 ************',
  email: '<EMAIL>',
  address: 'القاهرة، مصر',
  workingHours: 'السبت - الخميس: 9:00 ص - 6:00 م'
}

// Feature Flags
export const FEATURES = {
  enableWishlist: true,
  enableReviews: true,
  enableSocialLogin: true,
  enableGuestCheckout: true,
  enableMultiCurrency: false,
  enableMultiLanguage: false,
  enableLiveChat: true,
  enablePushNotifications: true
}

// Validation Rules
export const VALIDATION = {
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false
  },
  phone: {
    pattern: /^01[0-9]{9}$/, // Egyptian phone number pattern
    message: 'رقم الهاتف يجب أن يبدأ بـ 01 ويتكون من 11 رقم'
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'البريد الإلكتروني غير صحيح'
  }
}

// Cache Keys
export const CACHE_KEYS = {
  products: 'products',
  categories: 'categories',
  cart: 'cart',
  user: 'user',
  wishlist: 'wishlist',
  recentlyViewed: 'recently-viewed'
}

// Local Storage Keys
export const STORAGE_KEYS = {
  cart: 'greatcart_cart',
  wishlist: 'greatcart_wishlist',
  recentlyViewed: 'greatcart_recently_viewed',
  user: 'greatcart_user',
  theme: 'greatcart_theme',
  language: 'greatcart_language'
}

// Error Messages
export const ERROR_MESSAGES = {
  network: 'خطأ في الاتصال بالشبكة',
  server: 'خطأ في الخادم',
  notFound: 'الصفحة غير موجودة',
  unauthorized: 'غير مصرح لك بالوصول',
  forbidden: 'ممنوع الوصول',
  validation: 'خطأ في البيانات المدخلة',
  unknown: 'حدث خطأ غير متوقع'
}
