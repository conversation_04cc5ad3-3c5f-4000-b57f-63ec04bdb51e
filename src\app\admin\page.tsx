'use client'

import { useEffect, useState } from 'react'
import { 
  ShoppingBagIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'

interface DashboardStats {
  totalOrders: number
  totalRevenue: number
  totalCustomers: number
  totalProducts: number
  ordersGrowth: number
  revenueGrowth: number
  customersGrowth: number
  productsGrowth: number
}

interface RecentOrder {
  id: string
  orderNumber: string
  customerName: string
  total: number
  status: string
  createdAt: string
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      setStats({
        totalOrders: 1234,
        totalRevenue: 125000,
        totalCustomers: 856,
        totalProducts: 245,
        ordersGrowth: 12.5,
        revenueGrowth: 8.3,
        customersGrowth: 15.2,
        productsGrowth: 5.1,
      })

      setRecentOrders([
        {
          id: '1',
          orderNumber: 'ORD-001',
          customerName: 'أحمد محمد',
          total: 299,
          status: 'CONFIRMED',
          createdAt: '2023-12-01T10:00:00Z'
        },
        {
          id: '2',
          orderNumber: 'ORD-002',
          customerName: 'فاطمة علي',
          total: 599,
          status: 'SHIPPED',
          createdAt: '2023-12-01T09:30:00Z'
        },
        {
          id: '3',
          orderNumber: 'ORD-003',
          customerName: 'محمد أحمد',
          total: 149,
          status: 'PENDING',
          createdAt: '2023-12-01T09:00:00Z'
        },
      ])

      setLoading(false)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  const statCards = [
    {
      name: 'إجمالي الطلبات',
      value: stats?.totalOrders.toLocaleString('ar-EG') || '0',
      growth: stats?.ordersGrowth || 0,
      icon: ShoppingBagIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'إجمالي الإيرادات',
      value: formatCurrency(stats?.totalRevenue || 0),
      growth: stats?.revenueGrowth || 0,
      icon: CurrencyDollarIcon,
      color: 'bg-green-500',
    },
    {
      name: 'إجمالي العملاء',
      value: stats?.totalCustomers.toLocaleString('ar-EG') || '0',
      growth: stats?.customersGrowth || 0,
      icon: UsersIcon,
      color: 'bg-purple-500',
    },
    {
      name: 'إجمالي المنتجات',
      value: stats?.totalProducts.toLocaleString('ar-EG') || '0',
      growth: stats?.productsGrowth || 0,
      icon: ShoppingBagIcon,
      color: 'bg-orange-500',
    },
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800' },
      CONFIRMED: { label: 'مؤكد', color: 'bg-blue-100 text-blue-800' },
      SHIPPED: { label: 'تم الشحن', color: 'bg-purple-100 text-purple-800' },
      DELIVERED: { label: 'تم التسليم', color: 'bg-green-100 text-green-800' },
      CANCELLED: { label: 'ملغي', color: 'bg-red-100 text-red-800' },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <div key={stat.name} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-lg ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4 flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {stat.growth >= 0 ? (
                <ArrowUpIcon className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ${stat.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(stat.growth)}%
              </span>
              <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Orders */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">الطلبات الأخيرة</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رقم الطلب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  العميل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {order.orderNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.customerName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(order.total)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(order.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(order.createdAt).toLocaleDateString('ar-EG')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="px-6 py-3 border-t border-gray-200">
          <a href="/admin/orders" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
            عرض جميع الطلبات ←
          </a>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
          <div className="space-y-3">
            <a
              href="/admin/products/new"
              className="block w-full bg-primary-600 text-white text-center py-2 px-4 rounded-lg hover:bg-primary-700"
            >
              إضافة منتج جديد
            </a>
            <a
              href="/admin/orders"
              className="block w-full bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-lg hover:bg-gray-200"
            >
              إدارة الطلبات
            </a>
            <a
              href="/admin/customers"
              className="block w-full bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-lg hover:bg-gray-200"
            >
              إدارة العملاء
            </a>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">تنبيهات</h3>
          <div className="space-y-3">
            <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="mr-3">
                <p className="text-sm text-yellow-800">5 منتجات نفدت من المخزن</p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-blue-50 rounded-lg">
              <div className="flex-shrink-0">
                <ShoppingBagIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="mr-3">
                <p className="text-sm text-blue-800">12 طلب جديد في الانتظار</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">إحصائيات اليوم</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">طلبات جديدة</span>
              <span className="text-sm font-medium text-gray-900">23</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">مبيعات اليوم</span>
              <span className="text-sm font-medium text-gray-900">{formatCurrency(5420)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">زوار جدد</span>
              <span className="text-sm font-medium text-gray-900">156</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
