import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 بدء إدخال البيانات الأولية...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'مدير',
      lastName: 'النظام',
      password: hashedPassword,
      role: 'ADMIN',
      emailVerified: new Date(),
    },
  })

  console.log('✅ تم إنشاء المدير:', admin.email)

  // Create categories
  const categories = [
    {
      name: 'إلكترونيات',
      slug: 'electronics',
      description: 'أجهزة إلكترونية وتقنية',
      icon: '📱',
    },
    {
      name: 'ملابس',
      slug: 'clothing',
      description: 'ملاب<PERSON> رجالية ونسائية',
      icon: '👕',
    },
    {
      name: 'منزل وحديقة',
      slug: 'home-garden',
      description: 'أثاث ومستلزمات منزلية',
      icon: '🏠',
    },
    {
      name: 'رياضة',
      slug: 'sports',
      description: 'معدات ومستلزمات رياضية',
      icon: '⚽',
    },
    {
      name: 'جمال وعناية',
      slug: 'beauty',
      description: 'منتجات التجميل والعناية',
      icon: '💄',
    },
    {
      name: 'كتب',
      slug: 'books',
      description: 'كتب ومراجع',
      icon: '📚',
    },
  ]

  const createdCategories = []
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdCategories.push(created)
  }

  console.log('✅ تم إنشاء الفئات:', createdCategories.length)

  // Create products
  const products = [
    {
      name: 'سماعات لاسلكية عالية الجودة',
      slug: 'wireless-headphones-premium',
      description: 'سماعات لاسلكية متطورة تقدم جودة صوت استثنائية مع تقنية إلغاء الضوضاء النشطة',
      shortDesc: 'سماعات لاسلكية مع إلغاء الضوضاء',
      price: 299.0,
      originalPrice: 399.0,
      sku: 'WH-001',
      stock: 50,
      isFeatured: true,
      brand: 'TechPro',
      tags: 'سماعات,لاسلكي,بلوتوث',
      categoryId: createdCategories[0].id, // Electronics
    },
    {
      name: 'ساعة ذكية متطورة',
      slug: 'smart-watch-advanced',
      description: 'ساعة ذكية مع مراقبة الصحة وإشعارات ذكية',
      shortDesc: 'ساعة ذكية مع مراقبة الصحة',
      price: 599.0,
      originalPrice: 799.0,
      sku: 'SW-001',
      stock: 30,
      isFeatured: true,
      brand: 'SmartTech',
      tags: 'ساعة,ذكية,صحة',
      categoryId: createdCategories[0].id, // Electronics
    },
    {
      name: 'حقيبة ظهر عملية وأنيقة',
      slug: 'backpack-practical-elegant',
      description: 'حقيبة ظهر مصممة للاستخدام اليومي مع جيوب متعددة',
      shortDesc: 'حقيبة ظهر للاستخدام اليومي',
      price: 149.0,
      sku: 'BP-001',
      stock: 75,
      brand: 'StyleBag',
      tags: 'حقيبة,ظهر,عملية',
      categoryId: createdCategories[1].id, // Clothing
    },
    {
      name: 'كاميرا رقمية احترافية',
      slug: 'digital-camera-professional',
      description: 'كاميرا رقمية عالية الدقة للمصورين المحترفين',
      shortDesc: 'كاميرا رقمية عالية الدقة',
      price: 1299.0,
      originalPrice: 1599.0,
      sku: 'CAM-001',
      stock: 15,
      isFeatured: true,
      brand: 'PhotoPro',
      tags: 'كاميرا,تصوير,احترافية',
      categoryId: createdCategories[0].id, // Electronics
    },
    {
      name: 'جهاز لوحي متقدم',
      slug: 'tablet-advanced',
      description: 'جهاز لوحي بشاشة عالية الدقة ومعالج قوي',
      shortDesc: 'جهاز لوحي عالي الأداء',
      price: 899.0,
      sku: 'TAB-001',
      stock: 25,
      brand: 'TabletTech',
      tags: 'جهاز لوحي,تابلت,تقنية',
      categoryId: createdCategories[0].id, // Electronics
    },
  ]

  const createdProducts = []
  for (const product of products) {
    const created = await prisma.product.upsert({
      where: { slug: product.slug },
      update: {},
      create: product,
    })
    createdProducts.push(created)

    // Add product images
    await prisma.productImage.createMany({
      data: [
        {
          productId: created.id,
          url: `/images/items/${createdProducts.length}.jpg`,
          alt: product.name,
          sortOrder: 0,
        },
      ],
    })
  }

  console.log('✅ تم إنشاء المنتجات:', createdProducts.length)

  // Create sample customers
  const customers = [
    {
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'محمد',
      password: await bcrypt.hash('password123', 12),
      phone: '01234567890',
    },
    {
      email: '<EMAIL>',
      firstName: 'فاطمة',
      lastName: 'علي',
      password: await bcrypt.hash('password123', 12),
      phone: '01234567891',
    },
    {
      email: '<EMAIL>',
      firstName: 'محمد',
      lastName: 'أحمد',
      password: await bcrypt.hash('password123', 12),
      phone: '01234567892',
    },
  ]

  const createdCustomers = []
  for (const customer of customers) {
    const created = await prisma.user.upsert({
      where: { email: customer.email },
      update: {},
      create: customer,
    })
    createdCustomers.push(created)
  }

  console.log('✅ تم إنشاء العملاء:', createdCustomers.length)

  // Create sample coupons
  const coupons = [
    {
      code: 'WELCOME10',
      type: 'PERCENTAGE' as const,
      value: 10.0,
      minAmount: 100.0,
      usageLimit: 100,
      isActive: true,
      expiresAt: new Date('2024-12-31'),
    },
    {
      code: 'SAVE50',
      type: 'FIXED' as const,
      value: 50.0,
      minAmount: 200.0,
      usageLimit: 50,
      isActive: true,
      expiresAt: new Date('2024-06-30'),
    },
  ]

  for (const coupon of coupons) {
    await prisma.coupon.upsert({
      where: { code: coupon.code },
      update: {},
      create: coupon,
    })
  }

  console.log('✅ تم إنشاء كوبونات الخصم:', coupons.length)

  console.log('🎉 تم إدخال جميع البيانات الأولية بنجاح!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ خطأ في إدخال البيانات:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
