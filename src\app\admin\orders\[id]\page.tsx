'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { 
  ArrowLeftIcon,
  PrinterIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  MapPinIcon,
  CreditCardIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'

interface OrderItem {
  id: string
  name: string
  price: number
  quantity: number
  image: string
  sku: string
}

interface OrderDetails {
  id: string
  orderNumber: string
  status: string
  paymentStatus: string
  customer: {
    name: string
    email: string
    phone: string
  }
  shippingAddress: {
    name: string
    address: string
    city: string
    phone: string
  }
  billingAddress: {
    name: string
    address: string
    city: string
    phone: string
  }
  items: OrderItem[]
  subtotal: number
  shipping: number
  tax: number
  discount: number
  total: number
  createdAt: string
  notes?: string
}

export default function OrderDetails({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [order, setOrder] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchOrderDetails()
  }, [params.id])

  const fetchOrderDetails = async () => {
    setLoading(true)
    // Mock API call
    setTimeout(() => {
      const mockOrder: OrderDetails = {
        id: params.id,
        orderNumber: 'ORD-2023-001',
        status: 'CONFIRMED',
        paymentStatus: 'PAID',
        customer: {
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          phone: '+20 ************'
        },
        shippingAddress: {
          name: 'أحمد محمد علي',
          address: 'شارع النيل، المعادي',
          city: 'القاهرة، مصر',
          phone: '+20 ************'
        },
        billingAddress: {
          name: 'أحمد محمد علي',
          address: 'شارع النيل، المعادي',
          city: 'القاهرة، مصر',
          phone: '+20 ************'
        },
        items: [
          {
            id: '1',
            name: 'سماعات لاسلكية عالية الجودة',
            price: 299,
            quantity: 1,
            image: '/images/items/1.jpg',
            sku: 'WH-001'
          },
          {
            id: '2',
            name: 'ساعة ذكية متطورة',
            price: 599,
            quantity: 1,
            image: '/images/items/2.jpg',
            sku: 'SW-001'
          }
        ],
        subtotal: 898,
        shipping: 50,
        tax: 0,
        discount: 50,
        total: 898,
        createdAt: '2023-12-01T10:30:00Z',
        notes: 'يرجى التوصيل في المساء بعد الساعة 6 مساءً'
      }
      setOrder(mockOrder)
      setLoading(false)
    }, 1000)
  }

  const handleStatusChange = async (newStatus: string) => {
    if (order) {
      setOrder({ ...order, status: newStatus })
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { label: string; color: string; icon: any }> = {
      PENDING: { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800', icon: XCircleIcon },
      CONFIRMED: { label: 'مؤكد', color: 'bg-blue-100 text-blue-800', icon: CheckCircleIcon },
      PROCESSING: { label: 'قيد التجهيز', color: 'bg-purple-100 text-purple-800', icon: XCircleIcon },
      SHIPPED: { label: 'تم الشحن', color: 'bg-indigo-100 text-indigo-800', icon: TruckIcon },
      DELIVERED: { label: 'تم التسليم', color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
      CANCELLED: { label: 'ملغي', color: 'bg-red-100 text-red-800', icon: XCircleIcon }
    }

    const config = statusConfig[status] || statusConfig.PENDING
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        <Icon className="w-4 h-4 ml-1" />
        {config.label}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">الطلب غير موجود</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <button
            onClick={() => router.back()}
            className="p-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              طلب رقم {order.orderNumber}
            </h1>
            <p className="text-gray-600">
              تم الإنشاء في {new Date(order.createdAt).toLocaleDateString('ar-EG')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4 space-x-reverse">
          <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2 space-x-reverse">
            <PrinterIcon className="w-5 h-5" />
            <span>طباعة</span>
          </button>
          <select
            value={order.status}
            onChange={(e) => handleStatusChange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="PENDING">في الانتظار</option>
            <option value="CONFIRMED">مؤكد</option>
            <option value="PROCESSING">قيد التجهيز</option>
            <option value="SHIPPED">تم الشحن</option>
            <option value="DELIVERED">تم التسليم</option>
            <option value="CANCELLED">ملغي</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">حالة الطلب</h3>
              {getStatusBadge(order.status)}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">حالة الدفع</p>
                <p className="font-medium">
                  {order.paymentStatus === 'PAID' ? 'مدفوع' : 'في الانتظار'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">المبلغ الإجمالي</p>
                <p className="font-medium text-lg">{formatCurrency(order.total)}</p>
              </div>
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">منتجات الطلب</h3>
            
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item.id} className="flex items-center space-x-4 space-x-reverse border-b border-gray-200 pb-4">
                  <Image
                    src={item.image}
                    alt={item.name}
                    width={64}
                    height={64}
                    className="w-16 h-16 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.name}</h4>
                    <p className="text-sm text-gray-500">SKU: {item.sku}</p>
                    <p className="text-sm text-gray-500">الكمية: {item.quantity}</p>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">{formatCurrency(item.price)}</p>
                    <p className="text-sm text-gray-500">
                      المجموع: {formatCurrency(item.price * item.quantity)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="mt-6 border-t border-gray-200 pt-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي</span>
                  <span>{formatCurrency(order.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">الشحن</span>
                  <span>{formatCurrency(order.shipping)}</span>
                </div>
                {order.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>الخصم</span>
                    <span>-{formatCurrency(order.discount)}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t border-gray-200 pt-2">
                  <span>المجموع الإجمالي</span>
                  <span>{formatCurrency(order.total)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {order.notes && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">ملاحظات العميل</h3>
              <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{order.notes}</p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <UserIcon className="w-5 h-5 ml-2" />
              معلومات العميل
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <UserIcon className="w-4 h-4 text-gray-400" />
                <span className="text-gray-900">{order.customer.name}</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <EnvelopeIcon className="w-4 h-4 text-gray-400" />
                <a href={`mailto:${order.customer.email}`} className="text-primary-600 hover:text-primary-700">
                  {order.customer.email}
                </a>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <PhoneIcon className="w-4 h-4 text-gray-400" />
                <a href={`tel:${order.customer.phone}`} className="text-primary-600 hover:text-primary-700">
                  {order.customer.phone}
                </a>
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <MapPinIcon className="w-5 h-5 ml-2" />
              عنوان الشحن
            </h3>
            
            <div className="text-gray-700">
              <p className="font-medium">{order.shippingAddress.name}</p>
              <p>{order.shippingAddress.address}</p>
              <p>{order.shippingAddress.city}</p>
              <p className="mt-2 text-sm">
                <PhoneIcon className="w-4 h-4 inline ml-1" />
                {order.shippingAddress.phone}
              </p>
            </div>
          </div>

          {/* Billing Address */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <CreditCardIcon className="w-5 h-5 ml-2" />
              عنوان الفوترة
            </h3>
            
            <div className="text-gray-700">
              <p className="font-medium">{order.billingAddress.name}</p>
              <p>{order.billingAddress.address}</p>
              <p>{order.billingAddress.city}</p>
              <p className="mt-2 text-sm">
                <PhoneIcon className="w-4 h-4 inline ml-1" />
                {order.billingAddress.phone}
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
            
            <div className="space-y-3">
              {order.status === 'CONFIRMED' && (
                <button
                  onClick={() => handleStatusChange('PROCESSING')}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  بدء التجهيز
                </button>
              )}
              
              {order.status === 'PROCESSING' && (
                <button
                  onClick={() => handleStatusChange('SHIPPED')}
                  className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <TruckIcon className="w-4 h-4" />
                  <span>تم الشحن</span>
                </button>
              )}
              
              {order.status === 'SHIPPED' && (
                <button
                  onClick={() => handleStatusChange('DELIVERED')}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <CheckCircleIcon className="w-4 h-4" />
                  <span>تم التسليم</span>
                </button>
              )}
              
              <button className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 flex items-center justify-center space-x-2 space-x-reverse">
                <PrinterIcon className="w-4 h-4" />
                <span>طباعة الفاتورة</span>
              </button>
              
              <button className="w-full bg-red-100 text-red-700 py-2 px-4 rounded-lg hover:bg-red-200">
                إلغاء الطلب
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
