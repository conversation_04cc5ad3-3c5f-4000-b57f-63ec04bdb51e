'use client'

import { useState, useEffect } from 'react'
import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface CartNotificationProps {
  message: string
  type: 'success' | 'error'
  onClose: () => void
}

export default function CartNotification({ message, type, onClose }: CartNotificationProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(onClose, 300) // Wait for animation to complete
    }, 3000)

    return () => clearTimeout(timer)
  }, [onClose])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(onClose, 300)
  }

  return (
    <div
      className={`fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border transition-all duration-300 ${
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      }`}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {type === 'success' ? (
              <CheckCircleIcon className="h-6 w-6 text-green-400" />
            ) : (
              <XMarkIcon className="h-6 w-6 text-red-400" />
            )}
          </div>
          <div className="mr-3 w-0 flex-1">
            <p className="text-sm font-medium text-gray-900">{message}</p>
          </div>
          <div className="mr-4 flex-shrink-0 flex">
            <button
              onClick={handleClose}
              className="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook to manage notifications
export function useCartNotifications() {
  const [notifications, setNotifications] = useState<Array<{
    id: string
    message: string
    type: 'success' | 'error'
  }>>([])

  useEffect(() => {
    const handleCartUpdate = (event: CustomEvent) => {
      const notification = {
        id: Date.now().toString(),
        message: event.detail.message,
        type: event.detail.type
      }
      
      setNotifications(prev => [...prev, notification])
    }

    window.addEventListener('cart-updated', handleCartUpdate as EventListener)
    
    return () => {
      window.removeEventListener('cart-updated', handleCartUpdate as EventListener)
    }
  }, [])

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  return {
    notifications,
    removeNotification
  }
}
