const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 بدء إدخال البيانات الأولية...')

  // Create categories
  const electronics = await prisma.category.upsert({
    where: { slug: 'electronics' },
    update: {},
    create: {
      name: 'إلكترونيات',
      slug: 'electronics',
      description: 'أجهزة إلكترونية وتقنية',
      icon: '📱',
    },
  })

  const clothing = await prisma.category.upsert({
    where: { slug: 'clothing' },
    update: {},
    create: {
      name: 'ملابس',
      slug: 'clothing',
      description: 'ملابس رجالية ونسائية',
      icon: '👕',
    },
  })

  console.log('✅ تم إنشاء الفئات')

  // Create products
  const product1 = await prisma.product.upsert({
    where: { slug: 'wireless-headphones-premium' },
    update: {},
    create: {
      name: 'سماعات لاسلكية عالية الجودة',
      slug: 'wireless-headphones-premium',
      description: 'سماعات لاسلكية متطورة تقدم جودة صوت استثنائية',
      shortDesc: 'سماعات لاسلكية مع إلغاء الضوضاء',
      price: 299.0,
      originalPrice: 399.0,
      sku: 'WH-001',
      stock: 50,
      isFeatured: true,
      brand: 'TechPro',
      tags: 'سماعات,لاسلكي,بلوتوث',
      categoryId: electronics.id,
    },
  })

  const product2 = await prisma.product.upsert({
    where: { slug: 'smart-watch-advanced' },
    update: {},
    create: {
      name: 'ساعة ذكية متطورة',
      slug: 'smart-watch-advanced',
      description: 'ساعة ذكية مع مراقبة الصحة وإشعارات ذكية',
      shortDesc: 'ساعة ذكية مع مراقبة الصحة',
      price: 599.0,
      originalPrice: 799.0,
      sku: 'SW-001',
      stock: 30,
      isFeatured: true,
      brand: 'SmartTech',
      tags: 'ساعة,ذكية,صحة',
      categoryId: electronics.id,
    },
  })

  console.log('✅ تم إنشاء المنتجات')

  // Add product images
  await prisma.productImage.createMany({
    data: [
      {
        productId: product1.id,
        url: '/images/items/1.jpg',
        alt: 'سماعات لاسلكية',
        sortOrder: 0,
      },
      {
        productId: product2.id,
        url: '/images/items/2.jpg',
        alt: 'ساعة ذكية',
        sortOrder: 0,
      },
    ],
  })

  console.log('✅ تم إضافة صور المنتجات')
  console.log('🎉 تم إدخال جميع البيانات الأولية بنجاح!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ خطأ في إدخال البيانات:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
