# دليل إعداد وتشغيل تطبيق التجارة الإلكترونية

## نظرة عامة
تم إنشاء تطبيق تجارة إلكترونية متطور ومتجاوب باستخدام:
- **Next.js 14** مع App Router
- **React 18** مع TypeScript
- **Tailwind CSS** للتصميم
- **Heroicons** للأيقونات
- دعم كامل للغة العربية (RTL)

## المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بتحميل وتثبيت Node.js من: https://nodejs.org/
- تأكد من أن الإصدار 18.0 أو أحدث مثبت
- للتحقق من الإصدار:
```bash
node --version
npm --version
```

### 2. محرر النصوص
- يُنصح باستخدام Visual Studio Code
- قم بتثبيت الإضافات التالية:
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - TypeScript Importer
  - Auto Rename Tag

## خطوات التشغيل

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. تشغيل المشروع في وضع التطوير
```bash
npm run dev
```

### 3. فتح التطبيق
- افتح المتصفح وانتقل إلى: `http://localhost:3000`
- يجب أن تظهر الصفحة الرئيسية للتطبيق

### 4. بناء المشروع للإنتاج
```bash
npm run build
npm start
```

## هيكل المشروع

```
src/
├── app/                    # صفحات التطبيق (App Router)
│   ├── page.tsx           # الصفحة الرئيسية
│   ├── store/             # صفحة المتجر
│   ├── product/[id]/      # صفحة تفاصيل المنتج
│   ├── cart/              # صفحة سلة التسوق
│   ├── signin/            # صفحة تسجيل الدخول
│   ├── register/          # صفحة التسجيل
│   ├── layout.tsx         # التخطيط الرئيسي
│   └── globals.css        # الأنماط العامة
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── Header.tsx         # رأس الصفحة
│   ├── Footer.tsx         # تذييل الصفحة
│   ├── Layout.tsx         # مكون التخطيط
│   ├── ProductCard.tsx    # بطاقة المنتج
│   ├── SearchBar.tsx      # شريط البحث
│   ├── Pagination.tsx     # التنقل بين الصفحات
│   ├── Rating.tsx         # نظام التقييم
│   ├── Badge.tsx          # الشارات
│   ├── Button.tsx         # الأزرار
│   ├── Toast.tsx          # الإشعارات
│   └── LoadingSpinner.tsx # مؤشر التحميل
├── lib/                   # المكتبات والوظائف المساعدة
│   ├── utils.ts           # وظائف مساعدة
│   └── constants.ts       # الثوابت والإعدادات
├── types/                 # تعريفات الأنواع
│   └── index.ts           # الأنواع المشتركة
└── public/               # الملفات الثابتة
    └── images/           # الصور
```

## الصفحات المتاحة

1. **الصفحة الرئيسية** (`/`)
   - عرض المنتجات المميزة
   - الفئات الرئيسية
   - البانر الترويجي

2. **المتجر** (`/store`)
   - عرض جميع المنتجات
   - فلاتر البحث والتصنيف
   - ترتيب المنتجات

3. **تفاصيل المنتج** (`/product/[id]`)
   - معرض الصور
   - تفاصيل المنتج والمواصفات
   - التقييمات والمراجعات

4. **سلة التسوق** (`/cart`)
   - إدارة المنتجات في السلة
   - حساب المجموع والخصومات
   - كوبونات الخصم

5. **تسجيل الدخول** (`/signin`)
   - نموذج تسجيل الدخول
   - تسجيل الدخول عبر وسائل التواصل

6. **التسجيل** (`/register`)
   - إنشاء حساب جديد
   - التحقق من صحة البيانات

## المميزات المطبقة

### ✅ التصميم والواجهة
- [x] تصميم متجاوب مع جميع الأجهزة
- [x] دعم اللغة العربية (RTL)
- [x] واجهة مستخدم حديثة
- [x] تأثيرات بصرية سلسة

### ✅ ميزات التسوق
- [x] عرض المنتجات في شبكة
- [x] فلترة وترتيب المنتجات
- [x] صفحة تفاصيل المنتج
- [x] سلة تسوق تفاعلية
- [x] نظام المفضلة
- [x] كوبونات الخصم

### ✅ المكونات التفاعلية
- [x] شريط بحث ذكي
- [x] نظام التقييم
- [x] التنقل بين الصفحات
- [x] الإشعارات (Toast)
- [x] مؤشرات التحميل

### ✅ إدارة المستخدمين
- [x] نماذج تسجيل الدخول والتسجيل
- [x] التحقق من صحة البيانات
- [x] واجهات المستخدم

## التخصيص والتطوير

### تخصيص الألوان
يمكن تعديل الألوان في `tailwind.config.js`:
```javascript
colors: {
  primary: {
    // ألوان مخصصة
  }
}
```

### إضافة صفحات جديدة
1. أنشئ مجلد جديد في `src/app/`
2. أضف ملف `page.tsx`
3. استخدم مكون `Layout` للتخطيط

### إضافة مكونات جديدة
1. أنشئ ملف جديد في `src/components/`
2. استخدم TypeScript للأنواع
3. اتبع نمط التسمية المتسق

## استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في تثبيت التبعيات**: احذف `node_modules` و `package-lock.json` ثم أعد التثبيت
2. **مشاكل في الصور**: تأكد من وجود الصور في `public/images/`
3. **أخطاء TypeScript**: تحقق من الأنواع في `src/types/`

### أوامر مفيدة:
```bash
# تنظيف وإعادة تثبيت
rm -rf node_modules package-lock.json
npm install

# فحص الأخطاء
npm run lint

# بناء المشروع
npm run build
```

## الخطوات التالية

### للتطوير الإضافي:
1. **قاعدة البيانات**: ربط قاعدة بيانات حقيقية
2. **المصادقة**: تطبيق نظام مصادقة كامل
3. **الدفع**: ربط بوابات الدفع
4. **الإدارة**: لوحة تحكم للإدارة
5. **API**: تطوير واجهات برمجة التطبيقات
6. **التحسين**: تحسين الأداء والسيو

### للنشر:
1. **Vercel**: `npm run build` ثم رفع على Vercel
2. **Netlify**: ربط مع GitHub للنشر التلقائي
3. **خادم مخصص**: استخدام Docker للنشر

## الدعم والمساعدة

للحصول على المساعدة:
1. راجع الوثائق الرسمية لـ Next.js
2. تحقق من مجتمع React العربي
3. استخدم GitHub Issues للمشاكل التقنية

---

تم إنشاء هذا التطبيق بعناية ليكون نقطة انطلاق ممتازة لمشاريع التجارة الإلكترونية باللغة العربية. 🚀
