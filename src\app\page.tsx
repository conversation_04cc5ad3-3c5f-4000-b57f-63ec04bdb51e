'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Layout from '@/components/Layout'
import ProductCard from '@/components/ProductCard'

// Mock data for products
const featuredProducts = [
  {
    id: 1,
    name: 'سماعات لاسلكية عالية الجودة',
    price: 299,
    originalPrice: 399,
    image: '/images/items/1.jpg',
    rating: 4.5,
    reviews: 128,
    isNew: true,
    isOnSale: true,
    isFavorite: false
  },
  {
    id: 2,
    name: 'ساعة ذكية متطورة',
    price: 599,
    originalPrice: 799,
    image: '/images/items/2.jpg',
    rating: 4.8,
    reviews: 89,
    isNew: false,
    isOnSale: true,
    isFavorite: true
  },
  {
    id: 3,
    name: 'حقيبة ظهر عملية وأنيقة',
    price: 149,
    image: '/images/items/3.jpg',
    rating: 4.2,
    reviews: 45,
    isNew: true,
    isOnSale: false,
    isFavorite: false
  },
  {
    id: 4,
    name: 'كاميرا رقمية احترافية',
    price: 1299,
    originalPrice: 1599,
    image: '/images/items/4.jpg',
    rating: 4.9,
    reviews: 234,
    isNew: false,
    isOnSale: true,
    isFavorite: false
  },
  {
    id: 5,
    name: 'جهاز لوحي متقدم',
    price: 899,
    image: '/images/items/5.jpg',
    rating: 4.6,
    reviews: 156,
    isNew: true,
    isOnSale: false,
    isFavorite: true
  },
  {
    id: 6,
    name: 'مكبر صوت محمول',
    price: 199,
    originalPrice: 249,
    image: '/images/items/6.jpg',
    rating: 4.3,
    reviews: 67,
    isNew: false,
    isOnSale: true,
    isFavorite: false
  },
  {
    id: 7,
    name: 'لابتوب عالي الأداء',
    price: 2499,
    originalPrice: 2999,
    image: '/images/items/7.jpg',
    rating: 4.7,
    reviews: 198,
    isNew: true,
    isOnSale: true,
    isFavorite: false
  },
  {
    id: 8,
    name: 'هاتف ذكي متطور',
    price: 1899,
    image: '/images/items/8.jpg',
    rating: 4.8,
    reviews: 312,
    isNew: true,
    isOnSale: false,
    isFavorite: true
  }
]

const categories = [
  { id: 1, name: 'إلكترونيات', icon: '📱', count: 1250 },
  { id: 2, name: 'ملابس', icon: '👕', count: 890 },
  { id: 3, name: 'منزل وحديقة', icon: '🏠', count: 567 },
  { id: 4, name: 'رياضة', icon: '⚽', count: 423 },
  { id: 5, name: 'جمال وعناية', icon: '💄', count: 334 },
  { id: 6, name: 'كتب', icon: '📚', count: 789 }
]

export default function HomePage() {
  const [cartItems, setCartItems] = useState<number[]>([])
  const [favorites, setFavorites] = useState<number[]>([2, 5, 8])

  const handleAddToCart = (productId: number) => {
    setCartItems(prev => [...prev, productId])
    // Here you would typically show a toast notification
    console.log(`Product ${productId} added to cart`)
  }

  const handleToggleFavorite = (productId: number) => {
    setFavorites(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  return (
    <Layout>
      {/* Hero Banner */}
      <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="text-center lg:text-right">
              <h1 className="text-4xl lg:text-6xl font-bold mb-6">
                أفضل منصة تسوق إلكترونية
              </h1>
              <p className="text-xl mb-8 opacity-90">
                اكتشف آلاف المنتجات بأفضل الأسعار وأعلى جودة
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/store" className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  تسوق الآن
                </Link>
                <Link href="/categories" className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                  تصفح الفئات
                </Link>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/images/banners/1.jpg"
                alt="Hero Banner"
                width={600}
                height={400}
                className="rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">تسوق حسب الفئة</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              اكتشف مجموعة واسعة من المنتجات في فئات متنوعة
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.id}`}
                className="group bg-gray-50 rounded-lg p-6 text-center hover:bg-primary-50 hover:border-primary-200 border border-transparent transition-all duration-300"
              >
                <div className="text-4xl mb-3">{category.icon}</div>
                <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 mb-1">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-500">
                  {category.count.toLocaleString('ar-EG')} منتج
                </p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">المنتجات المميزة</h2>
              <p className="text-gray-600">أحدث وأفضل المنتجات المختارة خصيصاً لك</p>
            </div>
            <Link href="/store" className="btn-outline hidden md:block">
              عرض الكل
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={{
                  ...product,
                  isFavorite: favorites.includes(product.id)
                }}
                onAddToCart={handleAddToCart}
                onToggleFavorite={handleToggleFavorite}
              />
            ))}
          </div>

          <div className="text-center mt-8 md:hidden">
            <Link href="/store" className="btn-primary">
              عرض جميع المنتجات
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">شحن مجاني</h3>
              <p className="text-gray-600">شحن مجاني لجميع الطلبات أكثر من 500 ج.م</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">ضمان الجودة</h3>
              <p className="text-gray-600">ضمان استرداد الأموال خلال 30 يوم</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944l8.618 3.04A12.02 12.02 0 0021 9c0 5.591-3.824 10.29-9 11.622C6.824 19.29 3 14.591 3 9a12.02 12.02 0 00.382-3.016L12 2.944z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">دعم 24/7</h3>
              <p className="text-gray-600">خدمة عملاء متاحة على مدار الساعة</p>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  )
}
