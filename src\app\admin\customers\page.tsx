'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { 
  EyeIcon,
  EnvelopeIcon,
  PhoneIcon,
  MagnifyingGlassIcon,
  UserPlusIcon,
  BanknotesIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'

interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  avatar?: string
  totalOrders: number
  totalSpent: number
  lastOrderDate?: string
  status: 'active' | 'inactive'
  createdAt: string
}

export default function CustomersManagement() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)

  useEffect(() => {
    fetchCustomers()
  }, [currentPage, searchTerm, statusFilter])

  const fetchCustomers = async () => {
    setLoading(true)
    // Mock API call
    setTimeout(() => {
      const mockCustomers: Customer[] = [
        {
          id: '1',
          firstName: 'أحمد',
          lastName: 'محمد علي',
          email: '<EMAIL>',
          phone: '+20 ************',
          avatar: '/images/avatars/1.jpg',
          totalOrders: 12,
          totalSpent: 3450,
          lastOrderDate: '2023-12-01T10:30:00Z',
          status: 'active',
          createdAt: '2023-01-15T08:00:00Z'
        },
        {
          id: '2',
          firstName: 'فاطمة',
          lastName: 'أحمد حسن',
          email: '<EMAIL>',
          phone: '+20 ************',
          totalOrders: 8,
          totalSpent: 2100,
          lastOrderDate: '2023-11-28T14:20:00Z',
          status: 'active',
          createdAt: '2023-02-20T10:30:00Z'
        },
        {
          id: '3',
          firstName: 'محمد',
          lastName: 'حسن علي',
          email: '<EMAIL>',
          phone: '+20 ************',
          totalOrders: 15,
          totalSpent: 5200,
          lastOrderDate: '2023-12-01T09:15:00Z',
          status: 'active',
          createdAt: '2023-01-10T12:00:00Z'
        },
        {
          id: '4',
          firstName: 'سارة',
          lastName: 'علي محمد',
          email: '<EMAIL>',
          phone: '+20 ************',
          totalOrders: 3,
          totalSpent: 890,
          lastOrderDate: '2023-11-15T16:45:00Z',
          status: 'active',
          createdAt: '2023-10-05T14:20:00Z'
        },
        {
          id: '5',
          firstName: 'خالد',
          lastName: 'أحمد محمد',
          email: '<EMAIL>',
          totalOrders: 0,
          totalSpent: 0,
          status: 'inactive',
          createdAt: '2023-11-20T09:30:00Z'
        }
      ]
      setCustomers(mockCustomers)
      setLoading(false)
    }, 1000)
  }

  const handleStatusToggle = async (customerId: string) => {
    setCustomers(customers.map(customer => 
      customer.id === customerId 
        ? { ...customer, status: customer.status === 'active' ? 'inactive' : 'active' }
        : customer
    ))
  }

  const getStatusBadge = (status: string) => {
    return status === 'active' ? (
      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">نشط</span>
    ) : (
      <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">غير نشط</span>
    )
  }

  const getCustomerLevel = (totalSpent: number) => {
    if (totalSpent >= 5000) return { label: 'VIP', color: 'bg-purple-100 text-purple-800' }
    if (totalSpent >= 2000) return { label: 'ذهبي', color: 'bg-yellow-100 text-yellow-800' }
    if (totalSpent >= 500) return { label: 'فضي', color: 'bg-gray-100 text-gray-800' }
    return { label: 'برونزي', color: 'bg-orange-100 text-orange-800' }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة العملاء</h1>
          <p className="text-gray-600">عرض وإدارة بيانات العملاء</p>
        </div>
        <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2 space-x-reverse">
          <UserPlusIcon className="w-5 h-5" />
          <span>إضافة عميل جديد</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-blue-100 rounded-lg">
              <UserPlusIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">إجمالي العملاء</p>
              <p className="text-2xl font-bold text-gray-900">1,234</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-green-100 rounded-lg">
              <UserPlusIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">عملاء نشطون</p>
              <p className="text-2xl font-bold text-gray-900">1,156</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-purple-100 rounded-lg">
              <BanknotesIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">متوسط الإنفاق</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(2340)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-orange-100 rounded-lg">
              <ShoppingBagIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">متوسط الطلبات</p>
              <p className="text-2xl font-bold text-gray-900">8.5</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث بالاسم أو البريد الإلكتروني..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">جميع العملاء</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>

          {/* Sort */}
          <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            <option value="newest">الأحدث</option>
            <option value="oldest">الأقدم</option>
            <option value="most-orders">الأكثر طلبات</option>
            <option value="highest-spent">الأعلى إنفاقاً</option>
          </select>
        </div>
      </div>

      {/* Customers Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  العميل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  معلومات الاتصال
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الطلبات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  إجمالي الإنفاق
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستوى
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  آخر طلب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {customers.map((customer) => {
                const level = getCustomerLevel(customer.totalSpent)
                return (
                  <tr key={customer.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12">
                          {customer.avatar ? (
                            <Image
                              src={customer.avatar}
                              alt={`${customer.firstName} ${customer.lastName}`}
                              width={48}
                              height={48}
                              className="h-12 w-12 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-500 font-medium">
                                {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900">
                            {customer.firstName} {customer.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            عضو منذ {new Date(customer.createdAt).toLocaleDateString('ar-EG')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-gray-900">
                          <EnvelopeIcon className="w-4 h-4 ml-2 text-gray-400" />
                          {customer.email}
                        </div>
                        {customer.phone && (
                          <div className="flex items-center text-sm text-gray-500">
                            <PhoneIcon className="w-4 h-4 ml-2 text-gray-400" />
                            {customer.phone}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.totalOrders}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(customer.totalSpent)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${level.color}`}>
                        {level.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {customer.lastOrderDate ? (
                        new Date(customer.lastOrderDate).toLocaleDateString('ar-EG')
                      ) : (
                        'لا يوجد'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(customer.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Link
                          href={`/admin/customers/${customer.id}`}
                          className="text-primary-600 hover:text-primary-900"
                          title="عرض التفاصيل"
                        >
                          <EyeIcon className="w-4 h-4" />
                        </Link>
                        <button
                          className="text-blue-600 hover:text-blue-900"
                          title="إرسال رسالة"
                        >
                          <EnvelopeIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleStatusToggle(customer.id)}
                          className={`${
                            customer.status === 'active' 
                              ? 'text-red-600 hover:text-red-900' 
                              : 'text-green-600 hover:text-green-900'
                          }`}
                          title={customer.status === 'active' ? 'إيقاف' : 'تفعيل'}
                        >
                          {customer.status === 'active' ? 'إيقاف' : 'تفعيل'}
                        </button>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
          <div className="flex-1 flex justify-between sm:hidden">
            <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              السابق
            </button>
            <button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              التالي
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                عرض <span className="font-medium">1</span> إلى <span className="font-medium">5</span> من{' '}
                <span className="font-medium">{customers.length}</span> نتيجة
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  السابق
                </button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                  1
                </button>
                <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  التالي
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
