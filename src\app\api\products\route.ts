import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('12'),
  category: z.string().optional(),
  search: z.string().optional(),
  minPrice: z.string().optional(),
  maxPrice: z.string().optional(),
  sortBy: z.enum(['newest', 'price-low', 'price-high', 'rating', 'popular']).optional().default('newest'),
  featured: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    // Use nextUrl instead of request.url for static generation compatibility
    const { searchParams } = request.nextUrl
    const query = querySchema.parse(Object.fromEntries(searchParams))
    
    const page = parseInt(query.page)
    const limit = parseInt(query.limit)
    const skip = (page - 1) * limit

    // Mock products data for demo
    const mockProducts = [
      {
        id: '1',
        name: 'سماعات لاسلكية عالية الجودة',
        description: 'سماعات لاسلكية متطورة مع تقنية إلغاء الضوضاء',
        price: 299,
        originalPrice: 399,
        image: '/images/items/1.jpg',
        category: { name: 'إلكترونيات', slug: 'electronics' },
        averageRating: 4.5,
        reviewCount: 128,
        salesCount: 45,
        isActive: true,
        isFeatured: true,
        tags: ['سماعات', 'لاسلكي', 'بلوتوث']
      },
      {
        id: '2',
        name: 'ساعة ذكية متطورة',
        description: 'ساعة ذكية مع مراقبة الصحة والرياضة',
        price: 599,
        originalPrice: 799,
        image: '/images/items/2.jpg',
        category: { name: 'إلكترونيات', slug: 'electronics' },
        averageRating: 4.8,
        reviewCount: 89,
        salesCount: 32,
        isActive: true,
        isFeatured: true,
        tags: ['ساعة', 'ذكية', 'رياضة']
      },
      {
        id: '3',
        name: 'حقيبة ظهر عملية وأنيقة',
        description: 'حقيبة ظهر مقاومة للماء مع تصميم عصري',
        price: 149,
        originalPrice: null,
        image: '/images/items/3.jpg',
        category: { name: 'حقائب', slug: 'bags' },
        averageRating: 4.2,
        reviewCount: 67,
        salesCount: 78,
        isActive: true,
        isFeatured: false,
        tags: ['حقيبة', 'ظهر', 'مقاومة للماء']
      },
      {
        id: '4',
        name: 'كاميرا رقمية احترافية',
        description: 'كاميرا رقمية عالية الدقة للمصورين المحترفين',
        price: 1299,
        originalPrice: 1599,
        image: '/images/items/4.jpg',
        category: { name: 'إلكترونيات', slug: 'electronics' },
        averageRating: 4.9,
        reviewCount: 156,
        salesCount: 23,
        isActive: true,
        isFeatured: true,
        tags: ['كاميرا', 'تصوير', 'احترافية']
      }
    ]

    // Filter products based on query
    let filteredProducts = mockProducts.filter(product => {
      if (query.category && product.category.slug !== query.category) return false
      if (query.search && !product.name.includes(query.search) && !product.description.includes(query.search)) return false
      if (query.minPrice && product.price < parseFloat(query.minPrice)) return false
      if (query.maxPrice && product.price > parseFloat(query.maxPrice)) return false
      if (query.featured === 'true' && !product.isFeatured) return false
      return true
    })

    // Sort products
    switch (query.sortBy) {
      case 'price-low':
        filteredProducts.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filteredProducts.sort((a, b) => b.price - a.price)
        break
      case 'rating':
        filteredProducts.sort((a, b) => b.averageRating - a.averageRating)
        break
      case 'popular':
        filteredProducts.sort((a, b) => b.salesCount - a.salesCount)
        break
    }

    const total = filteredProducts.length
    const productsWithRatings = filteredProducts.slice(skip, skip + limit)

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: productsWithRatings,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error('Error fetching products:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: 'خطأ في المعاملات',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب المنتجات'
    }, { status: 500 })
  }
}
