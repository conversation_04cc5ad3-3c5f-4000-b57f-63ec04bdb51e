import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import FacebookProvider from 'next-auth/providers/facebook'

export const authOptions: NextAuthOptions = {
  session: {
    strategy: 'jwt',
  },
  pages: {
    signIn: '/signin',
    signUp: '/register',
  },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Mock users for demo (remove in production)
        const mockUsers = [
          {
            id: '1',
            email: '<EMAIL>',
            password: 'admin123',
            firstName: 'المدير',
            lastName: 'العام',
            role: 'ADMIN',
            avatar: null,
          },
          {
            id: '2',
            email: '<EMAIL>',
            password: 'password123',
            firstName: 'أحمد',
            lastName: 'محمد',
            role: 'CUSTOMER',
            avatar: null,
          }
        ]

        // Check mock users first (for demo)
        const mockUser = mockUsers.find(
          u => u.email === credentials.email && u.password === credentials.password
        )

        if (mockUser) {
          return {
            id: mockUser.id,
            email: mockUser.email,
            firstName: mockUser.firstName,
            lastName: mockUser.lastName,
            role: mockUser.role,
            avatar: mockUser.avatar,
          }
        }

        // Database authentication disabled for demo
        return null
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        return {
          ...token,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
        }
      }
      return token
    },
    async session({ session, token }) {
      return {
        ...session,
        user: {
          ...session.user,
          id: token.sub,
          role: token.role,
          firstName: token.firstName,
          lastName: token.lastName,
        }
      }
    },
  },
}

// Helper functions for authentication (disabled for demo)
// These would be used with a real database

export async function registerUser(userData: {
  email: string
  password: string
  firstName: string
  lastName: string
}) {
  // Mock registration for demo
  // In production, this would hash the password and save to database
  return {
    id: Date.now().toString(),
    email: userData.email,
    firstName: userData.firstName,
    lastName: userData.lastName,
    role: 'CUSTOMER',
    avatar: null,
  }
}
