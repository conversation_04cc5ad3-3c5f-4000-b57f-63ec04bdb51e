{"name": "ecommerce-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.6.0", "bcryptjs": "^2.4.3", "clsx": "^2.0.0", "date-fns": "^2.30.0", "jsonwebtoken": "^9.0.2", "next": "14.0.0", "next-auth": "^4.24.5", "nodemailer": "^6.9.7", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "stripe": "^14.7.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "prisma": "^6.8.2", "tailwindcss": "^3.3.0", "typescript": "^5"}}