import { NextRequest, NextResponse } from 'next/server'
import { verifyWebhookSignature } from '@/lib/stripe'
import { db } from '@/lib/db'
import <PERSON><PERSON> from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')

    if (!signature) {
      return NextResponse.json({
        success: false,
        message: 'Missing signature'
      }, { status: 400 })
    }

    // Verify webhook signature
    const event = verifyWebhookSignature(body, signature)

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSuccess(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailure(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object as Stripe.Dispute)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json({
      success: false,
      message: 'Webhook error'
    }, { status: 400 })
  }
}

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Update payment status
    const payment = await db.payment.findFirst({
      where: { transactionId: paymentIntent.id },
      include: { order: true }
    })

    if (payment) {
      // Update payment status
      await db.payment.update({
        where: { id: payment.id },
        data: {
          status: 'PAID',
          gatewayData: {
            ...payment.gatewayData as object,
            paymentIntent,
          }
        }
      })

      // Update order status
      await db.order.update({
        where: { id: payment.orderId },
        data: {
          paymentStatus: 'PAID',
          status: 'CONFIRMED'
        }
      })

      // TODO: Send confirmation email
      console.log(`Payment successful for order: ${payment.order.orderNumber}`)
    }
  } catch (error) {
    console.error('Error handling payment success:', error)
  }
}

async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Update payment status
    const payment = await db.payment.findFirst({
      where: { transactionId: paymentIntent.id }
    })

    if (payment) {
      await db.payment.update({
        where: { id: payment.id },
        data: {
          status: 'FAILED',
          gatewayData: {
            ...payment.gatewayData as object,
            paymentIntent,
          }
        }
      })

      // Update order status
      await db.order.update({
        where: { id: payment.orderId },
        data: {
          paymentStatus: 'FAILED'
        }
      })

      console.log(`Payment failed for payment: ${payment.id}`)
    }
  } catch (error) {
    console.error('Error handling payment failure:', error)
  }
}

async function handleChargeDispute(dispute: Stripe.Dispute) {
  try {
    // Handle charge dispute
    console.log(`Charge dispute created: ${dispute.id}`)
    
    // TODO: Notify admin about dispute
    // TODO: Update order status if needed
  } catch (error) {
    console.error('Error handling charge dispute:', error)
  }
}
