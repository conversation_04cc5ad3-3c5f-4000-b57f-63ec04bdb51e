import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createPaymentIntent } from '@/lib/stripe'
import { db } from '@/lib/db'
import { z } from 'zod'

const createIntentSchema = z.object({
  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),
  orderId: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: 'يجب تسجيل الدخول أولاً'
      }, { status: 401 })
    }

    const body = await request.json()
    const { amount, orderId } = createIntentSchema.parse(body)

    // Create payment intent
    const paymentIntent = await createPaymentIntent(amount)

    // Save payment record if orderId is provided
    if (orderId) {
      await db.payment.create({
        data: {
          amount,
          status: 'PENDING',
          method: 'STRIPE',
          transactionId: paymentIntent.id,
          orderId,
          gatewayData: {
            clientSecret: paymentIntent.client_secret,
            paymentIntentId: paymentIntent.id,
          }
        }
      })
    }

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    })

  } catch (error) {
    console.error('Error creating payment intent:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: 'خطأ في البيانات المدخلة',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إنشاء عملية الدفع'
    }, { status: 500 })
  }
}
