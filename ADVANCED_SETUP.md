# دليل الإعداد المتقدم - تطبيق التجارة الإلكترونية

## 🚀 نظرة عامة على الميزات المتقدمة

تم تطوير التطبيق ليشمل الميزات التالية:

### ✅ قاعدة البيانات
- **Prisma ORM** مع PostgreSQL
- مخطط قاعدة بيانات شامل
- علاقات معقدة بين الجداول
- فهرسة محسنة للأداء

### ✅ نظام المصادقة
- **NextAuth.js** للمصادقة
- تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
- تسجيل الدخول عبر Google و Facebook
- إدارة الجلسات والأدوار

### ✅ بوابات الدفع
- **Stripe** للمدفوعات الإلكترونية
- دعم البطاقات الائتمانية
- Webhooks للتحديثات التلقائية
- إدارة المبالغ المستردة

### ✅ API متكامل
- RESTful API endpoints
- التحقق من صحة البيانات مع Zod
- معالجة الأخطاء المتقدمة
- توثيق شامل

### ✅ لوحة تحكم الإدارة
- إحصائيات شاملة
- إدارة المنتجات والطلبات
- إدارة العملاء
- تقارير المبيعات

## 🛠️ متطلبات النظام

### البرامج المطلوبة:
1. **Node.js** (الإصدار 18 أو أحدث)
2. **PostgreSQL** (الإصدار 13 أو أحدث)
3. **Git** لإدارة الإصدارات

### الحسابات المطلوبة:
1. **Stripe Account** للمدفوعات
2. **Google Cloud Console** (اختياري - للتسجيل عبر Google)
3. **Facebook Developers** (اختياري - للتسجيل عبر Facebook)

## 📋 خطوات الإعداد التفصيلية

### 1. إعداد قاعدة البيانات

#### تثبيت PostgreSQL:

**على Windows:**
1. حمل PostgreSQL من: https://www.postgresql.org/download/windows/
2. قم بالتثبيت واتبع التعليمات
3. احفظ كلمة مرور المستخدم `postgres`

**على macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**على Ubuntu/Linux:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### إنشاء قاعدة البيانات:
```bash
# الدخول إلى PostgreSQL
sudo -u postgres psql

# إنشاء قاعدة البيانات
CREATE DATABASE greatcart_db;

# إنشاء مستخدم جديد
CREATE USER greatcart_user WITH PASSWORD 'your_password';

# منح الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE greatcart_db TO greatcart_user;

# الخروج
\q
```

### 2. إعداد متغيرات البيئة

```bash
# نسخ ملف البيئة
cp .env.example .env.local

# تحرير الملف
nano .env.local
```

**محتوى ملف `.env.local`:**
```env
# Database
DATABASE_URL="postgresql://greatcart_user:your_password@localhost:5432/greatcart_db"

# Authentication
NEXTAUTH_SECRET="your-super-secret-key-here-minimum-32-characters"
NEXTAUTH_URL="http://localhost:3000"

# Stripe (احصل على المفاتيح من dashboard.stripe.com)
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Google OAuth (اختياري)
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# Facebook OAuth (اختياري)
FACEBOOK_CLIENT_ID="your_facebook_client_id"
FACEBOOK_CLIENT_SECRET="your_facebook_client_secret"
```

### 3. تثبيت التبعيات وإعداد قاعدة البيانات

```bash
# تثبيت التبعيات
npm install

# إنشاء جداول قاعدة البيانات
npx prisma db push

# إدخال البيانات الأولية
npm run db:seed

# إنشاء Prisma Client
npx prisma generate
```

### 4. إعداد Stripe

#### إنشاء حساب Stripe:
1. اذهب إلى: https://dashboard.stripe.com/register
2. أنشئ حساب جديد
3. فعل حسابك

#### الحصول على مفاتيح API:
1. اذهب إلى **Developers > API keys**
2. انسخ **Publishable key** و **Secret key**
3. ضعهما في ملف `.env.local`

#### إعداد Webhooks:
1. اذهب إلى **Developers > Webhooks**
2. اضغط **Add endpoint**
3. URL: `http://localhost:3000/api/payments/webhook`
4. اختر الأحداث:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `charge.dispute.created`
5. انسخ **Signing secret** وضعه في `.env.local`

### 5. إعداد OAuth (اختياري)

#### Google OAuth:
1. اذهب إلى: https://console.cloud.google.com/
2. أنشئ مشروع جديد
3. فعل **Google+ API**
4. أنشئ **OAuth 2.0 credentials**
5. أضف `http://localhost:3000/api/auth/callback/google` كـ redirect URI

#### Facebook OAuth:
1. اذهب إلى: https://developers.facebook.com/
2. أنشئ تطبيق جديد
3. أضف **Facebook Login**
4. أضف `http://localhost:3000/api/auth/callback/facebook` كـ redirect URI

## 🚀 تشغيل التطبيق

### وضع التطوير:
```bash
npm run dev
```

### بناء للإنتاج:
```bash
npm run build
npm start
```

### أوامر قاعدة البيانات المفيدة:
```bash
# عرض قاعدة البيانات في المتصفح
npm run db:studio

# إعادة تعيين قاعدة البيانات
npx prisma db push --force-reset
npm run db:seed

# إنشاء migration جديد
npm run db:migrate
```

## 🔐 الوصول للنظام

### حساب المدير الافتراضي:
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`
- **الرابط:** `http://localhost:3000/admin`

### حسابات العملاء التجريبية:
- `<EMAIL>` / `password123`
- `<EMAIL>` / `password123`
- `<EMAIL>` / `password123`

## 📊 استخدام لوحة الإدارة

### الوصول للوحة الإدارة:
1. سجل الدخول بحساب المدير
2. اذهب إلى: `http://localhost:3000/admin`

### الميزات المتاحة:
- **لوحة التحكم:** إحصائيات شاملة
- **إدارة المنتجات:** إضافة وتعديل المنتجات
- **إدارة الطلبات:** متابعة حالة الطلبات
- **إدارة العملاء:** عرض بيانات العملاء
- **التقارير:** تقارير المبيعات والأداء

## 🧪 اختبار المدفوعات

### بطاقات اختبار Stripe:
```
# بطاقة ناجحة
4242 4242 4242 4242

# بطاقة فاشلة
4000 0000 0000 0002

# بطاقة تتطلب 3D Secure
4000 0025 0000 3155

# تاريخ انتهاء: أي تاريخ مستقبلي
# CVC: أي 3 أرقام
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ في الاتصال بقاعدة البيانات:
```bash
# تحقق من تشغيل PostgreSQL
sudo systemctl status postgresql

# تحقق من صحة DATABASE_URL
echo $DATABASE_URL
```

#### خطأ في Prisma:
```bash
# إعادة إنشاء Prisma Client
npx prisma generate

# إعادة تعيين قاعدة البيانات
npx prisma db push --force-reset
```

#### خطأ في المصادقة:
```bash
# تحقق من NEXTAUTH_SECRET
echo $NEXTAUTH_SECRET

# يجب أن يكون 32 حرف على الأقل
```

### سجلات الأخطاء:
```bash
# عرض سجلات التطبيق
npm run dev

# عرض سجلات قاعدة البيانات
tail -f /var/log/postgresql/postgresql-*.log
```

## 📈 تحسين الأداء

### قاعدة البيانات:
- إضافة فهارس للبحث السريع
- تحسين الاستعلامات
- استخدام Connection Pooling

### التطبيق:
- تفعيل Next.js Image Optimization
- استخدام Static Generation حيث أمكن
- تفعيل Caching

## 🚀 النشر للإنتاج

### Vercel (موصى به):
```bash
# تثبيت Vercel CLI
npm i -g vercel

# النشر
vercel

# ربط قاعدة البيانات (استخدم Supabase أو PlanetScale)
```

### Docker:
```bash
# بناء الصورة
docker build -t greatcart .

# تشغيل الحاوية
docker run -p 3000:3000 greatcart
```

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع الوثائق الرسمية
2. تحقق من GitHub Issues
3. استخدم مجتمع Discord

---

🎉 **تهانينا! لديك الآن تطبيق تجارة إلكترونية متكامل وجاهز للاستخدام!**
