import type { Metadata } from 'next'
import './globals.css'
import SessionProvider from '@/components/providers/SessionProvider'
import { CartProvider } from '@/contexts/CartContext'

export const metadata: Metadata = {
  title: 'جريت كارت | واحدة من أكبر منصات التسوق عبر الإنترنت',
  description: 'منصة تسوق إلكترونية متطورة تقدم أفضل المنتجات بأسعار تنافسية',
  keywords: 'تسوق, إلكترونيات, ملابس, منزل, حديقة',
  authors: [{ name: 'Great Cart Team' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="icon" href="/images/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      </head>
      <body className="font-arabic">
        <SessionProvider>
          <CartProvider>
            {children}
          </CartProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
