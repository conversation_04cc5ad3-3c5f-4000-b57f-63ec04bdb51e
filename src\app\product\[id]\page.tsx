'use client'

import { useState } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import Layout from '@/components/Layout'
import ProductCard from '@/components/ProductCard'
import { 
  HeartIcon, 
  ShoppingCartIcon, 
  StarIcon,
  TruckIcon,
  ShieldCheckIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

// Mock product data
const productData = {
  id: 1,
  name: 'سماعات لاسلكية عالية الجودة مع إلغاء الضوضاء',
  price: 299,
  originalPrice: 399,
  images: [
    '/images/items/1.jpg',
    '/images/items/2.jpg',
    '/images/items/3.jpg',
    '/images/items/4.jpg'
  ],
  rating: 4.5,
  reviews: 128,
  description: 'سماعات لاسلكية متطورة تقدم جودة صوت استثنائية مع تقنية إلغاء الضوضاء النشطة. مصممة للراحة طوال اليوم مع بطارية تدوم حتى 30 ساعة.',
  features: [
    'تقنية إلغاء الضوضاء النشطة',
    'بطارية تدوم حتى 30 ساعة',
    'اتصال بلوتوث 5.0',
    'مقاومة للماء IPX4',
    'شحن سريع - 15 دقيقة = 3 ساعات تشغيل',
    'ميكروفون مدمج عالي الجودة'
  ],
  specifications: {
    'نوع الاتصال': 'بلوتوث 5.0',
    'مدى الاتصال': '10 متر',
    'عمر البطارية': '30 ساعة',
    'وقت الشحن': '2 ساعة',
    'الوزن': '250 جرام',
    'الألوان المتاحة': 'أسود، أبيض، أزرق'
  },
  inStock: true,
  category: 'إلكترونيات',
  brand: 'TechPro',
  sku: 'TP-WH-001'
}

// Related products
const relatedProducts = [
  {
    id: 2,
    name: 'ساعة ذكية متطورة',
    price: 599,
    originalPrice: 799,
    image: '/images/items/2.jpg',
    rating: 4.8,
    reviews: 89,
    isNew: false,
    isOnSale: true,
    isFavorite: false
  },
  {
    id: 3,
    name: 'مكبر صوت محمول',
    price: 199,
    originalPrice: 249,
    image: '/images/items/6.jpg',
    rating: 4.3,
    reviews: 67,
    isNew: false,
    isOnSale: true,
    isFavorite: false
  },
  {
    id: 4,
    name: 'هاتف ذكي متطور',
    price: 1899,
    image: '/images/items/8.jpg',
    rating: 4.8,
    reviews: 312,
    isNew: true,
    isOnSale: false,
    isFavorite: false
  },
  {
    id: 5,
    name: 'جهاز لوحي متقدم',
    price: 899,
    image: '/images/items/5.jpg',
    rating: 4.6,
    reviews: 156,
    isNew: true,
    isOnSale: false,
    isFavorite: false
  }
]

export default function ProductPage() {
  const params = useParams()
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isFavorite, setIsFavorite] = useState(false)
  const [activeTab, setActiveTab] = useState('description')

  const discountPercentage = Math.round(((productData.originalPrice - productData.price) / productData.originalPrice) * 100)

  const handleAddToCart = () => {
    console.log(`Added ${quantity} items to cart`)
  }

  const handleBuyNow = () => {
    console.log('Buy now clicked')
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-primary-600">الرئيسية</Link>
          <span>/</span>
          <Link href="/store" className="hover:text-primary-600">المتجر</Link>
          <span>/</span>
          <Link href={`/category/${productData.category}`} className="hover:text-primary-600">{productData.category}</Link>
          <span>/</span>
          <span className="text-gray-900">{productData.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <div>
            <div className="relative aspect-square mb-4 bg-gray-100 rounded-lg overflow-hidden">
              <Image
                src={productData.images[selectedImage]}
                alt={productData.name}
                fill
                className="object-cover"
              />
              {productData.originalPrice && (
                <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  -{discountPercentage}%
                </div>
              )}
            </div>
            
            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-2">
              {productData.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square rounded-lg overflow-hidden border-2 ${
                    selectedImage === index ? 'border-primary-600' : 'border-gray-200'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${productData.name} ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <div className="mb-4">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{productData.name}</h1>
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(productData.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                  <span className="mr-2 text-sm text-gray-600">
                    ({productData.reviews} تقييم)
                  </span>
                </div>
                <span className="text-sm text-gray-500">SKU: {productData.sku}</span>
              </div>
            </div>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-center space-x-4 space-x-reverse mb-2">
                <span className="text-3xl font-bold text-gray-900">
                  {productData.price.toLocaleString('ar-EG')} ج.م
                </span>
                {productData.originalPrice && (
                  <span className="text-xl text-gray-500 line-through">
                    {productData.originalPrice.toLocaleString('ar-EG')} ج.م
                  </span>
                )}
              </div>
              {productData.originalPrice && (
                <p className="text-green-600 font-medium">
                  وفر {(productData.originalPrice - productData.price).toLocaleString('ar-EG')} ج.م
                </p>
              )}
            </div>

            {/* Stock Status */}
            <div className="mb-6">
              {productData.inStock ? (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  متوفر في المخزن
                </span>
              ) : (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                  غير متوفر
                </span>
              )}
            </div>

            {/* Quantity and Actions */}
            <div className="mb-8">
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <label className="text-sm font-medium text-gray-700">الكمية:</label>
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="px-3 py-2 text-gray-600 hover:text-gray-800"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="px-3 py-2 text-gray-600 hover:text-gray-800"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleAddToCart}
                  disabled={!productData.inStock}
                  className="flex-1 bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ShoppingCartIcon className="w-5 h-5" />
                  <span>أضف للسلة</span>
                </button>
                
                <button
                  onClick={handleBuyNow}
                  disabled={!productData.inStock}
                  className="flex-1 bg-gray-900 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  اشتري الآن
                </button>
                
                <button
                  onClick={() => setIsFavorite(!isFavorite)}
                  className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  {isFavorite ? (
                    <HeartSolidIcon className="w-6 h-6 text-red-500" />
                  ) : (
                    <HeartIcon className="w-6 h-6 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <div className="flex items-center space-x-3 space-x-reverse">
                <TruckIcon className="w-6 h-6 text-primary-600" />
                <div>
                  <p className="font-medium text-gray-900">شحن مجاني</p>
                  <p className="text-sm text-gray-600">للطلبات أكثر من 500 ج.م</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 space-x-reverse">
                <ArrowPathIcon className="w-6 h-6 text-primary-600" />
                <div>
                  <p className="font-medium text-gray-900">إرجاع مجاني</p>
                  <p className="text-sm text-gray-600">خلال 30 يوم</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 space-x-reverse">
                <ShieldCheckIcon className="w-6 h-6 text-primary-600" />
                <div>
                  <p className="font-medium text-gray-900">ضمان سنة</p>
                  <p className="text-sm text-gray-600">ضمان الشركة المصنعة</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mb-16">
          <div className="border-b border-gray-200 mb-8">
            <nav className="flex space-x-8 space-x-reverse">
              {[
                { id: 'description', label: 'الوصف' },
                { id: 'specifications', label: 'المواصفات' },
                { id: 'reviews', label: 'التقييمات' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-600 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="prose prose-lg max-w-none">
            {activeTab === 'description' && (
              <div>
                <p className="text-gray-700 mb-6">{productData.description}</p>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">المميزات الرئيسية:</h3>
                <ul className="space-y-2">
                  {productData.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(productData.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">{key}</span>
                      <span className="text-gray-700">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <div className="text-center py-12">
                  <p className="text-gray-500">التقييمات ستكون متاحة قريباً</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-8">منتجات ذات صلة</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                onAddToCart={() => console.log(`Added product ${product.id} to cart`)}
                onToggleFavorite={() => console.log(`Toggled favorite for product ${product.id}`)}
              />
            ))}
          </div>
        </div>
      </div>
    </Layout>
  )
}
