/* ملف CSS لدعم اللغة العربية واتجاه RTL */

/* تعيين اتجاه الصفحة من اليمين إلى اليسار */
body, html {
    direction: rtl;
    text-align: right;
    font-family: '<PERSON><PERSON><PERSON>', 'Inter', sans-serif;
}

/* تعديل اتجاه العناصر المختلفة */
.navbar-nav, .dropdown-menu, .list-inline, .list-check {
    padding-right: 0;
}

/* عكس الهوامش والحشوات */
.mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

.ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.mr-1, .mr-2, .mr-3, .mr-4, .mr-5 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
}

.ml-1, .ml-2, .ml-3, .ml-4, .ml-5 {
    margin-left: 0 !important;
    margin-right: 0.25rem !important;
}

.pr-1, .pr-2, .pr-3, .pr-4, .pr-5 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
}

.pl-1, .pl-2, .pl-3, .pl-4, .pl-5 {
    padding-left: 0 !important;
    padding-right: 0.25rem !important;
}

/* عكس اتجاه الأيقونات */
.fa-arrow-right:before {
    content: "\f060" !important;
}

.fa-arrow-left:before {
    content: "\f061" !important;
}

.fa-long-arrow-alt-right:before {
    content: "\f30a" !important;
}

.fa-long-arrow-alt-left:before {
    content: "\f30b" !important;
}

/* عكس اتجاه الأيقونات الإضافية (Chevrons, Angles) */
.fa-chevron-left:before {
    content: "\f054" !important; /* chevron-right */
}

.fa-chevron-right:before {
    content: "\f053" !important; /* chevron-left */
}

.fa-angle-left:before {
    content: "\f105" !important; /* angle-right */
}

.fa-angle-right:before {
    content: "\f104" !important; /* angle-left */
}

.fa-angle-double-left:before {
    content: "\f101" !important; /* angle-double-right */
}

.fa-angle-double-right:before {
    content: "\f100" !important; /* angle-double-left */
}

/* تعديل اتجاه عناصر القائمة المنسدلة */
.dropdown-menu {
    text-align: right;
}

/* تعديل اتجاه عناصر النموذج */
.form-control {
    text-align: right;
}

/* تعديل اتجاه الأزرار */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

/* تعديل اتجاه عناصر التنقل */
.navbar-nav {
    direction: rtl;
}

/* تعديل اتجاه عناصر البحث */
.input-group .form-control:not(:last-child) {
    border-radius: 0 0.25rem 0.25rem 0;
}

.input-group .input-group-append {
    margin-right: -1px;
    margin-left: 0;
}

.input-group-append .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* تعديل اتجاه عناصر السلة */
.widget-header .icon {
    margin-left: 10px;
    margin-right: 0;
}

/* تعديل اتجاه عناصر المنتجات */
.card-product-grid .info-wrap {
    text-align: right;
}

/* تعديل اتجاه عناصر التذييل */
.footer-bottom .nav-link {
    text-align: right;
}

/* تعديل اتجاه عناصر الصفحة الرئيسية */
.section-intro, .section-content {
    text-align: right;
}

/* تعديل اتجاه عناصر التنقل الجانبي */
.card-category-list .card-category {
    text-align: right;
}

/* تعديل اتجاه عناصر التصفية */
.filter-content {
    text-align: right;
}

/* تعديل اتجاه عناصر المنتج التفصيلية */
.gallery-wrap, .info-main, .info-aside {
    text-align: right;
}

/* تعديل اتجاه عناصر السلة */
.table-shopping-cart .price {
    text-align: left;
}

/* تعديل اتجاه عناصر الدفع */
.card-header .title {
    text-align: right;
}

/* تعديل اتجاه عناصر صفحة اكتمال الطلب */
.invoice-details .list-unstyled li strong {
    float: right;
    margin-left: 5px; /* لإضافة مسافة بين العنوان والقيمة */
}

.invoice-items .table th,
.invoice-items .table td {
    text-align: right !important;
}

/* الإبقاء على محاذاة أعمدة السعر إلى اليسار */
.invoice-items .table th.text-center:last-child,
.invoice-items .table td.text-center:last-child,
.invoice-items .table tfoot th:last-child {
    text-align: left !important;
}

/* تعديل محاذاة تذييل الفاتورة */
.invoice-footer p {
    text-align: right !important;
}

/* إضافة خط عربي */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');