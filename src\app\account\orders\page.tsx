'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import Layout from '@/components/Layout'
import { 
  EyeIcon,
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  date: string
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
  total: number
  itemsCount: number
  items: Array<{
    id: string
    name: string
    image: string
    quantity: number
    price: number
  }>
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchOrders()
  }, [])

  const fetchOrders = async () => {
    setLoading(true)
    // Mock API call
    setTimeout(() => {
      const mockOrders: Order[] = [
        {
          id: '1',
          orderNumber: 'ORD-2023-001',
          date: '2023-12-01',
          status: 'delivered',
          total: 1197,
          itemsCount: 3,
          items: [
            {
              id: '1',
              name: 'سماعات لاسلكية عالية الجودة',
              image: '/images/items/1.jpg',
              quantity: 2,
              price: 299
            },
            {
              id: '2',
              name: 'ساعة ذكية متطورة',
              image: '/images/items/2.jpg',
              quantity: 1,
              price: 599
            }
          ]
        },
        {
          id: '2',
          orderNumber: 'ORD-2023-002',
          date: '2023-11-28',
          status: 'shipped',
          total: 149,
          itemsCount: 1,
          items: [
            {
              id: '3',
              name: 'حقيبة ظهر عملية وأنيقة',
              image: '/images/items/3.jpg',
              quantity: 1,
              price: 149
            }
          ]
        },
        {
          id: '3',
          orderNumber: 'ORD-2023-003',
          date: '2023-11-25',
          status: 'confirmed',
          total: 1299,
          itemsCount: 1,
          items: [
            {
              id: '4',
              name: 'كاميرا رقمية احترافية',
              image: '/images/items/4.jpg',
              quantity: 1,
              price: 1299
            }
          ]
        }
      ]
      setOrders(mockOrders)
      setLoading(false)
    }, 1000)
  }

  const getStatusBadge = (status: Order['status']) => {
    const statusConfig = {
      pending: { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800', icon: ClockIcon },
      confirmed: { label: 'مؤكد', color: 'bg-blue-100 text-blue-800', icon: CheckCircleIcon },
      shipped: { label: 'تم الشحن', color: 'bg-purple-100 text-purple-800', icon: TruckIcon },
      delivered: { label: 'تم التسليم', color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
      cancelled: { label: 'ملغي', color: 'bg-red-100 text-red-800', icon: XCircleIcon }
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 ml-1" />
        {config.label}
      </span>
    )
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">طلباتي</h1>
          <p className="text-gray-600 mt-2">تتبع وإدارة طلباتك</p>
        </div>

        {orders.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📦</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات</h3>
            <p className="text-gray-600 mb-6">لم تقم بأي طلبات بعد</p>
            <Link
              href="/store"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700"
            >
              ابدأ التسوق
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <div key={order.id} className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
                {/* Order Header */}
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          طلب رقم: {order.orderNumber}
                        </h3>
                        <p className="text-sm text-gray-600">
                          تاريخ الطلب: {new Date(order.date).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                      {getStatusBadge(order.status)}
                    </div>
                    <div className="text-left">
                      <p className="text-lg font-bold text-gray-900">
                        {formatCurrency(order.total)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.itemsCount} منتج
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="p-6">
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4 space-x-reverse">
                        <Image
                          src={item.image}
                          alt={item.name}
                          width={60}
                          height={60}
                          className="w-15 h-15 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900">{item.name}</h4>
                          <p className="text-sm text-gray-500">الكمية: {item.quantity}</p>
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(item.price * item.quantity)}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Order Actions */}
                  <div className="mt-6 flex items-center justify-between border-t pt-4">
                    <div className="flex space-x-3 space-x-reverse">
                      <Link
                        href={`/account/orders/${order.id}`}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <EyeIcon className="w-4 h-4 ml-1" />
                        عرض التفاصيل
                      </Link>
                      
                      {order.status === 'delivered' && (
                        <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                          إعادة الطلب
                        </button>
                      )}
                      
                      {(order.status === 'pending' || order.status === 'confirmed') && (
                        <button className="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                          إلغاء الطلب
                        </button>
                      )}
                    </div>

                    {order.status === 'shipped' && (
                      <div className="text-sm text-gray-600">
                        <TruckIcon className="w-4 h-4 inline ml-1" />
                        تتبع الشحنة
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {orders.length > 0 && (
          <div className="mt-8 flex items-center justify-center">
            <nav className="flex items-center space-x-2 space-x-reverse">
              <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                السابق
              </button>
              <button className="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                1
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                2
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                التالي
              </button>
            </nav>
          </div>
        )}
      </div>
    </Layout>
  )
}
