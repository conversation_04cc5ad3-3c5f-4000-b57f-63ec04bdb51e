// Product Types
export interface Product {
  id: number
  name: string
  description?: string
  price: number
  originalPrice?: number
  image: string
  images?: string[]
  rating?: number
  reviews?: number
  category: string
  brand?: string
  sku?: string
  inStock: boolean
  isNew?: boolean
  isOnSale?: boolean
  isFavorite?: boolean
  features?: string[]
  specifications?: Record<string, string>
}

// Category Types
export interface Category {
  id: number
  name: string
  slug: string
  icon?: string
  image?: string
  count?: number
  parentId?: number
  children?: Category[]
}

// Cart Types
export interface CartItem {
  id: number
  productId: number
  name: string
  price: number
  originalPrice?: number
  image: string
  quantity: number
  inStock: boolean
}

export interface Cart {
  items: CartItem[]
  subtotal: number
  discount: number
  shipping: number
  total: number
}

// User Types
export interface User {
  id: number
  firstName: string
  lastName: string
  email: string
  phone?: string
  avatar?: string
  dateJoined: string
  isVerified: boolean
}

// Address Types
export interface Address {
  id: number
  userId: number
  type: 'home' | 'work' | 'other'
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state: string
  postalCode: string
  country: string
  phone?: string
  isDefault: boolean
}

// Order Types
export interface OrderItem {
  id: number
  productId: number
  name: string
  price: number
  quantity: number
  image: string
}

export interface Order {
  id: number
  userId: number
  orderNumber: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  items: OrderItem[]
  subtotal: number
  discount: number
  shipping: number
  tax: number
  total: number
  shippingAddress: Address
  billingAddress?: Address
  paymentMethod: string
  createdAt: string
  updatedAt: string
  estimatedDelivery?: string
}

// Review Types
export interface Review {
  id: number
  userId: number
  productId: number
  rating: number
  title?: string
  comment: string
  images?: string[]
  verified: boolean
  helpful: number
  createdAt: string
  user: {
    firstName: string
    lastName: string
    avatar?: string
  }
}

// Wishlist Types
export interface WishlistItem {
  id: number
  userId: number
  productId: number
  addedAt: string
  product: Product
}

// Filter Types
export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  rating?: number
  inStock?: boolean
  isNew?: boolean
  isOnSale?: boolean
  brand?: string[]
  sortBy?: 'newest' | 'price-low' | 'price-high' | 'rating' | 'popular'
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form Types
export interface LoginForm {
  email: string
  password: string
  rememberMe: boolean
}

export interface RegisterForm {
  firstName: string
  lastName: string
  email: string
  phone: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
  subscribeNewsletter: boolean
}

export interface ContactForm {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
}

// Search Types
export interface SearchResult {
  products: Product[]
  categories: Category[]
  total: number
  query: string
  suggestions?: string[]
}

// Notification Types
export interface Notification {
  id: number
  userId: number
  type: 'order' | 'promotion' | 'system' | 'reminder'
  title: string
  message: string
  read: boolean
  createdAt: string
  actionUrl?: string
}

// Payment Types
export interface PaymentMethod {
  id: number
  userId: number
  type: 'card' | 'paypal' | 'bank' | 'cash'
  name: string
  details: Record<string, any>
  isDefault: boolean
}

// Coupon Types
export interface Coupon {
  id: number
  code: string
  type: 'percentage' | 'fixed'
  value: number
  minAmount?: number
  maxDiscount?: number
  expiresAt?: string
  usageLimit?: number
  usageCount: number
  isActive: boolean
}
