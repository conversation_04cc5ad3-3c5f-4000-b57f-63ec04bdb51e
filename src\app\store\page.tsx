'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/Layout'
import ProductCard from '@/components/ProductCard'
import { 
  FunnelIcon, 
  Squares2X2Icon, 
  ListBulletIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  rating: number
  reviews: number
  category: string
  brand: string
  inStock: boolean
  isFeatured: boolean
}

interface Category {
  id: string
  name: string
  count: number
}

export default function StorePage() {
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('featured')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [priceRange, setPriceRange] = useState([0, 5000])
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  // Mock data
  useEffect(() => {
    fetchProducts()
    fetchCategories()
  }, [])

  const fetchProducts = async () => {
    setLoading(true)
    // Mock API call
    setTimeout(() => {
      const mockProducts: Product[] = [
        {
          id: '1',
          name: 'سماعات لاسلكية عالية الجودة',
          price: 299,
          originalPrice: 399,
          image: '/images/items/1.jpg',
          rating: 4.5,
          reviews: 128,
          category: 'إلكترونيات',
          brand: 'TechPro',
          inStock: true,
          isFeatured: true
        },
        {
          id: '2',
          name: 'ساعة ذكية متطورة',
          price: 599,
          originalPrice: 799,
          image: '/images/items/2.jpg',
          rating: 4.8,
          reviews: 89,
          category: 'إلكترونيات',
          brand: 'SmartTech',
          inStock: true,
          isFeatured: true
        },
        {
          id: '3',
          name: 'حقيبة ظهر عملية وأنيقة',
          price: 149,
          image: '/images/items/3.jpg',
          rating: 4.2,
          reviews: 45,
          category: 'حقائب',
          brand: 'StyleBag',
          inStock: false,
          isFeatured: false
        },
        {
          id: '4',
          name: 'كاميرا رقمية احترافية',
          price: 1299,
          originalPrice: 1599,
          image: '/images/items/4.jpg',
          rating: 4.9,
          reviews: 156,
          category: 'إلكترونيات',
          brand: 'PhotoPro',
          inStock: true,
          isFeatured: true
        },
        {
          id: '5',
          name: 'جهاز لوحي متقدم',
          price: 899,
          image: '/images/items/5.jpg',
          rating: 4.6,
          reviews: 203,
          category: 'إلكترونيات',
          brand: 'TabletMax',
          inStock: true,
          isFeatured: false
        },
        {
          id: '6',
          name: 'قميص قطني أنيق',
          price: 89,
          originalPrice: 120,
          image: '/images/items/6.jpg',
          rating: 4.3,
          reviews: 67,
          category: 'ملابس',
          brand: 'FashionStyle',
          inStock: true,
          isFeatured: false
        }
      ]
      setProducts(mockProducts)
      setLoading(false)
    }, 1000)
  }

  const fetchCategories = async () => {
    const mockCategories: Category[] = [
      { id: 'electronics', name: 'إلكترونيات', count: 45 },
      { id: 'clothing', name: 'ملابس', count: 32 },
      { id: 'bags', name: 'حقائب', count: 18 },
      { id: 'home', name: 'منزل وحديقة', count: 24 },
      { id: 'sports', name: 'رياضة', count: 15 },
      { id: 'beauty', name: 'جمال وعناية', count: 28 }
    ]
    setCategories(mockCategories)
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1]
    return matchesSearch && matchesCategory && matchesPrice
  })

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price
      case 'price-high':
        return b.price - a.price
      case 'rating':
        return b.rating - a.rating
      case 'newest':
        return b.id.localeCompare(a.id)
      default:
        return b.isFeatured ? 1 : -1
    }
  })

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">المتجر</h1>
                <p className="text-gray-600 mt-1">اكتشف أفضل المنتجات بأسعار رائعة</p>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="البحث في المنتجات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64 pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar Filters */}
            <div className="lg:w-64 flex-shrink-0">
              <div className="bg-white rounded-lg shadow p-6 sticky top-4">
                <div className="flex items-center justify-between mb-4 lg:hidden">
                  <h3 className="text-lg font-medium text-gray-900">الفلاتر</h3>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-400 hover:text-gray-600"
                  >
                    <FunnelIcon className="w-5 h-5" />
                  </button>
                </div>

                <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                  {/* Categories */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">الفئات</h4>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="category"
                          value="all"
                          checked={selectedCategory === 'all'}
                          onChange={(e) => setSelectedCategory(e.target.value)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                        />
                        <span className="mr-2 text-sm text-gray-700">جميع الفئات</span>
                      </label>
                      {categories.map(category => (
                        <label key={category.id} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <input
                              type="radio"
                              name="category"
                              value={category.name}
                              checked={selectedCategory === category.name}
                              onChange={(e) => setSelectedCategory(e.target.value)}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                            />
                            <span className="mr-2 text-sm text-gray-700">{category.name}</span>
                          </div>
                          <span className="text-xs text-gray-500">({category.count})</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Price Range */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">نطاق السعر</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{priceRange[0]} جنيه</span>
                        <span>{priceRange[1]} جنيه</span>
                      </div>
                      <input
                        type="range"
                        min="0"
                        max="5000"
                        step="50"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                    </div>
                  </div>

                  {/* Clear Filters */}
                  <button
                    onClick={() => {
                      setSelectedCategory('all')
                      setPriceRange([0, 5000])
                      setSearchTerm('')
                    }}
                    className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    مسح الفلاتر
                  </button>
                </div>
              </div>
            </div>

            {/* Products */}
            <div className="flex-1">
              {/* Toolbar */}
              <div className="bg-white rounded-lg shadow p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <span className="text-sm text-gray-600">
                      عرض {sortedProducts.length} من {products.length} منتج
                    </span>
                  </div>

                  <div className="flex items-center space-x-4 space-x-reverse">
                    {/* Sort */}
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="featured">المميزة</option>
                      <option value="newest">الأحدث</option>
                      <option value="price-low">السعر: من الأقل للأعلى</option>
                      <option value="price-high">السعر: من الأعلى للأقل</option>
                      <option value="rating">الأعلى تقييماً</option>
                    </select>

                    {/* View Mode */}
                    <div className="flex items-center border border-gray-300 rounded-lg">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`p-2 ${viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-400'}`}
                      >
                        <Squares2X2Icon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-2 ${viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-400'}`}
                      >
                        <ListBulletIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Products Grid/List */}
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                </div>
              ) : sortedProducts.length > 0 ? (
                <div className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                    : 'grid-cols-1'
                }`}>
                  {sortedProducts.map(product => (
                    <ProductCard 
                      key={product.id} 
                      product={product} 
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-6xl mb-4">🔍</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد منتجات</h3>
                  <p className="text-gray-600">جرب تغيير الفلاتر أو البحث بكلمات أخرى</p>
                </div>
              )}

              {/* Pagination */}
              {sortedProducts.length > 0 && (
                <div className="mt-8 flex items-center justify-center">
                  <nav className="flex items-center space-x-2 space-x-reverse">
                    <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                      السابق
                    </button>
                    <button className="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                      1
                    </button>
                    <button className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                      2
                    </button>
                    <button className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                      3
                    </button>
                    <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                      التالي
                    </button>
                  </nav>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
