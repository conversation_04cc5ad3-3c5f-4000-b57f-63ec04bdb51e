'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  EyeIcon,
  PrinterIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  customerName: string
  customerEmail: string
  total: number
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'
  itemsCount: number
  createdAt: string
  shippingAddress: string
}

export default function OrdersManagement() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)

  // Mock data
  useEffect(() => {
    fetchOrders()
  }, [currentPage, searchTerm, statusFilter])

  const fetchOrders = async () => {
    setLoading(true)
    // Mock API call
    setTimeout(() => {
      const mockOrders: Order[] = [
        {
          id: '1',
          orderNumber: 'ORD-2023-001',
          customerName: 'أحمد محمد علي',
          customerEmail: '<EMAIL>',
          total: 898,
          status: 'PENDING',
          paymentStatus: 'PENDING',
          itemsCount: 3,
          createdAt: '2023-12-01T10:30:00Z',
          shippingAddress: 'القاهرة، مصر'
        },
        {
          id: '2',
          orderNumber: 'ORD-2023-002',
          customerName: 'فاطمة أحمد',
          customerEmail: '<EMAIL>',
          total: 599,
          status: 'CONFIRMED',
          paymentStatus: 'PAID',
          itemsCount: 1,
          createdAt: '2023-12-01T09:15:00Z',
          shippingAddress: 'الجيزة، مصر'
        },
        {
          id: '3',
          orderNumber: 'ORD-2023-003',
          customerName: 'محمد حسن',
          customerEmail: '<EMAIL>',
          total: 1299,
          status: 'PROCESSING',
          paymentStatus: 'PAID',
          itemsCount: 2,
          createdAt: '2023-12-01T08:45:00Z',
          shippingAddress: 'الإسكندرية، مصر'
        },
        {
          id: '4',
          orderNumber: 'ORD-2023-004',
          customerName: 'سارة علي',
          customerEmail: '<EMAIL>',
          total: 149,
          status: 'SHIPPED',
          paymentStatus: 'PAID',
          itemsCount: 1,
          createdAt: '2023-11-30T16:20:00Z',
          shippingAddress: 'المنصورة، مصر'
        },
        {
          id: '5',
          orderNumber: 'ORD-2023-005',
          customerName: 'خالد أحمد',
          customerEmail: '<EMAIL>',
          total: 299,
          status: 'DELIVERED',
          paymentStatus: 'PAID',
          itemsCount: 1,
          createdAt: '2023-11-30T14:10:00Z',
          shippingAddress: 'أسوان، مصر'
        },
        {
          id: '6',
          orderNumber: 'ORD-2023-006',
          customerName: 'نور محمد',
          customerEmail: '<EMAIL>',
          total: 450,
          status: 'CANCELLED',
          paymentStatus: 'REFUNDED',
          itemsCount: 2,
          createdAt: '2023-11-30T12:00:00Z',
          shippingAddress: 'طنطا، مصر'
        }
      ]
      setOrders(mockOrders)
      setLoading(false)
    }, 1000)
  }

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    setOrders(orders.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus as Order['status'] }
        : order
    ))
  }

  const getStatusBadge = (status: Order['status']) => {
    const statusConfig = {
      PENDING: { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800', icon: ClockIcon },
      CONFIRMED: { label: 'مؤكد', color: 'bg-blue-100 text-blue-800', icon: CheckCircleIcon },
      PROCESSING: { label: 'قيد التجهيز', color: 'bg-purple-100 text-purple-800', icon: ClockIcon },
      SHIPPED: { label: 'تم الشحن', color: 'bg-indigo-100 text-indigo-800', icon: TruckIcon },
      DELIVERED: { label: 'تم التسليم', color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
      CANCELLED: { label: 'ملغي', color: 'bg-red-100 text-red-800', icon: XCircleIcon }
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 ml-1" />
        {config.label}
      </span>
    )
  }

  const getPaymentStatusBadge = (status: Order['paymentStatus']) => {
    const statusConfig = {
      PENDING: { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800' },
      PAID: { label: 'مدفوع', color: 'bg-green-100 text-green-800' },
      FAILED: { label: 'فشل', color: 'bg-red-100 text-red-800' },
      REFUNDED: { label: 'مسترد', color: 'bg-gray-100 text-gray-800' }
    }

    const config = statusConfig[status]

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الطلبات</h1>
          <p className="text-gray-600">متابعة وإدارة طلبات العملاء</p>
        </div>
        <div className="flex items-center space-x-4 space-x-reverse">
          <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2 space-x-reverse">
            <PrinterIcon className="w-5 h-5" />
            <span>طباعة التقرير</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-yellow-100 rounded-lg">
              <ClockIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">طلبات جديدة</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-blue-100 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">طلبات مؤكدة</p>
              <p className="text-2xl font-bold text-gray-900">28</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-indigo-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-indigo-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">قيد الشحن</p>
              <p className="text-2xl font-bold text-gray-900">15</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-green-100 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">تم التسليم</p>
              <p className="text-2xl font-bold text-gray-900">156</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث برقم الطلب أو اسم العميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">جميع الحالات</option>
            <option value="PENDING">في الانتظار</option>
            <option value="CONFIRMED">مؤكد</option>
            <option value="PROCESSING">قيد التجهيز</option>
            <option value="SHIPPED">تم الشحن</option>
            <option value="DELIVERED">تم التسليم</option>
            <option value="CANCELLED">ملغي</option>
          </select>

          {/* Date Filter */}
          <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            <option value="all">جميع التواريخ</option>
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
          </select>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رقم الطلب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  العميل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  حالة الطلب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  حالة الدفع
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {order.orderNumber}
                    </div>
                    <div className="text-sm text-gray-500">
                      {order.itemsCount} منتج
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {order.customerName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {order.customerEmail}
                    </div>
                    <div className="text-sm text-gray-500">
                      {order.shippingAddress}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(order.total)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <select
                      value={order.status}
                      onChange={(e) => handleStatusChange(order.id, e.target.value)}
                      className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary-500"
                    >
                      <option value="PENDING">في الانتظار</option>
                      <option value="CONFIRMED">مؤكد</option>
                      <option value="PROCESSING">قيد التجهيز</option>
                      <option value="SHIPPED">تم الشحن</option>
                      <option value="DELIVERED">تم التسليم</option>
                      <option value="CANCELLED">ملغي</option>
                    </select>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getPaymentStatusBadge(order.paymentStatus)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(order.createdAt).toLocaleDateString('ar-EG')}
                    <br />
                    {new Date(order.createdAt).toLocaleTimeString('ar-EG', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Link
                        href={`/admin/orders/${order.id}`}
                        className="text-primary-600 hover:text-primary-900"
                        title="عرض التفاصيل"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </Link>
                      <button
                        className="text-blue-600 hover:text-blue-900"
                        title="طباعة الفاتورة"
                      >
                        <PrinterIcon className="w-4 h-4" />
                      </button>
                      {order.status === 'CONFIRMED' && (
                        <button
                          onClick={() => handleStatusChange(order.id, 'PROCESSING')}
                          className="text-green-600 hover:text-green-900"
                          title="بدء التجهيز"
                        >
                          <CheckCircleIcon className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
          <div className="flex-1 flex justify-between sm:hidden">
            <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              السابق
            </button>
            <button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              التالي
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                عرض <span className="font-medium">1</span> إلى <span className="font-medium">6</span> من{' '}
                <span className="font-medium">{orders.length}</span> نتيجة
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  السابق
                </button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                  1
                </button>
                <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  التالي
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
